'use client';

import React from 'react';

// Custom arrow icons
const ChevronLeftIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 19l-7-7 7-7"
    />
  </svg>
);

const ChevronRightIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 5l7 7-7 7"
    />
  </svg>
);

interface CarouselNavigationProps {
  showNavigation: boolean;
  totalSlides: number;
  currentSlide: number;
  onPrevSlide: () => void;
  onNextSlide: () => void;
  onGoToSlide: (slideIndex: number) => void;
  arrowSize?: 'sm' | 'md' | 'lg';
  arrowPosition?: 'inside' | 'outside';
  showDisabledArrows?: boolean;
}

const CarouselNavigation: React.FC<CarouselNavigationProps> = ({
  showNavigation,
  totalSlides,
  currentSlide,
  onPrevSlide,
  onNextSlide,
  onGoToSlide,
  arrowSize = 'md',
  arrowPosition = 'inside',
  showDisabledArrows = false,
}) => {
  const getArrowSizeClasses = () => {
    switch (arrowSize) {
      case 'sm':
        return {
          button: 'p-2',
          icon: 'w-4 h-4',
        };
      case 'lg':
        return {
          button: 'p-4',
          icon: 'w-8 h-8',
        };
      default:
        return {
          button: 'p-3',
          icon: 'w-6 h-6',
        };
    }
  };

  const getArrowPositionClasses = () => {
    const baseClasses =
      'absolute top-1/2 transform -translate-y-1/2 z-20 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-gray-200 hover:bg-gray-50';

    switch (arrowPosition) {
      case 'outside':
        return {
          left: `${baseClasses} left-2`,
          right: `${baseClasses} right-2`,
        };
      default:
        return {
          left: `${baseClasses} left-4`,
          right: `${baseClasses} right-4`,
        };
    }
  };

  if (!showNavigation) return null;

  const sizeClasses = getArrowSizeClasses();
  const positionClasses = getArrowPositionClasses();

  const hasPrevSlide = currentSlide > 0;
  const hasNextSlide = currentSlide < totalSlides - 1;

  return (
    <>
      {/* Left Arrow */}
      {(showDisabledArrows || hasPrevSlide) && (
        <button
          onClick={onPrevSlide}
          disabled={!hasPrevSlide}
          className={`${positionClasses.left} ${!hasPrevSlide ? 'opacity-50 cursor-not-allowed' : ''}`}
          aria-label="Previous items"
        >
          <ChevronLeftIcon className={sizeClasses.icon} />
        </button>
      )}

      {/* Right Arrow */}
      {(showDisabledArrows || hasNextSlide) && (
        <button
          onClick={onNextSlide}
          disabled={!hasNextSlide}
          className={`${positionClasses.right} ${!hasNextSlide ? 'opacity-50 cursor-not-allowed' : ''}`}
          aria-label="Next items"
        >
          <ChevronRightIcon className={sizeClasses.icon} />
        </button>
      )}
    </>
  );
};

export default CarouselNavigation;
