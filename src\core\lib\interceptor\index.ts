import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';

import { getToken } from '@core/lib/auth/services';

import { useUserStore } from '@/store/userStore';

import { throttledUpdateLastActivity } from '@/utils/session';

import API_CONFIG from '@/core/configs/api';

const { API_URL, SUBSCRIPTION_KEY, API_BASE_URL } = API_CONFIG;

const pendingRequests = new Map();

const generateRequestKey = (config: InternalAxiosRequestConfig<unknown>) => {
  return `${config.method}:${config.url}:${JSON.stringify(config.params)}:${JSON.stringify(config.data)}`;
};

const createRequestInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use(
    async (config) => {
      throttledUpdateLastActivity();

      try {
        const token = await getToken();
        config.headers.set('Authorization', `Bearer ${token}`);

        // Ensure subscription key is always set
        if (SUBSCRIPTION_KEY) {
          config.headers.set('Ocp-Apim-Subscription-Key', SUBSCRIPTION_KEY);
        }

        if (config.method?.toLowerCase() === 'get') {
          const requestKey = generateRequestKey(config);
          if (pendingRequests.has(requestKey)) {
            const cancel = pendingRequests.get(requestKey);
            cancel();
          }
          const source = axios.CancelToken.source();
          config.cancelToken = source.token;
          pendingRequests.set(requestKey, source.cancel);
        }

        return config;
      } catch {
        // Even if token fetch fails, ensure subscription key is set
        if (SUBSCRIPTION_KEY) {
          config.headers.set('Ocp-Apim-Subscription-Key', SUBSCRIPTION_KEY);
        }
        return config;
      }
    },
    (error) => Promise.reject(error)
  );
};

const createResponseInterceptor = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.response.use(
    (response) => {
      const requestKey = generateRequestKey(response.config);
      pendingRequests.delete(requestKey);
      return response;
    },
    async (error) => {
      if (axios.isCancel(error)) {
        console.warn('Duplicate request canceled:', error.message);
        return new Promise(() => {});
      }

      // Handle subscription expiry - check response for expiry message
      if (error.response?.data) {
        const responseData = error.response.data;
        let expiryMessage = '';

        // Check if response is a string
        if (typeof responseData === 'string') {
          expiryMessage = responseData;
        }
        // Check if response is an object with message property
        else if (
          responseData.message &&
          typeof responseData.message === 'string'
        ) {
          expiryMessage = responseData.message;
        }

        // Check for subscription expiry message
        if (
          expiryMessage.includes(
            'Your subscription expired. Need to upgrade.'
          ) ||
          expiryMessage.toLowerCase().includes('subscription expired')
        ) {
          // Get accountType from store
          const userStore = useUserStore.getState();
          let accountType = userStore.data?.accountType;

          // If accountType is undefined, try to get existing value from localStorage
          // Do NOT set a default value - let the unauthorized page handle it
          if (!accountType && typeof window !== 'undefined') {
            const existingAccountType = localStorage.getItem(
              'subscriptionAccountType'
            );
            if (
              existingAccountType &&
              existingAccountType !== 'undefined' &&
              existingAccountType !== 'null'
            ) {
              accountType = existingAccountType;
            }
          }

          // Only store if we have a valid value (not undefined, null, or empty)
          if (accountType && typeof window !== 'undefined') {
            localStorage.setItem('subscriptionAccountType', accountType);
          }

          // Redirect to unauthorized page with subscription_expired reason
          // The unauthorized page will handle the display based on accountType
          if (typeof window !== 'undefined') {
            window.location.href = '/unauthorized?reason=subscription_expired';
          }

          // Return a resolved promise to prevent further error handling
          return new Promise(() => {});
        }
      }

      return Promise.reject(error);
    }
  );
};

const headers = {
  'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY,
};

export const arcaAxios = axios.create({
  baseURL: API_URL,
  headers,
});

createRequestInterceptor(arcaAxios);
createResponseInterceptor(arcaAxios);

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers,
});

createRequestInterceptor(api);
createResponseInterceptor(api);

// Public API instance without authentication for subscription endpoints
export const publicApi = axios.create({
  baseURL: API_BASE_URL,
  headers,
});
