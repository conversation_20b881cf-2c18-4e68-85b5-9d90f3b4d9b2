'use client';

import { useState, useEffect } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useUserStore } from '@/store/userStore';

import SignoutIcon from '@/assets/svg/SignoutIcon';
import WarningIcon from '@/assets/svg/Warning';
import YellowLockIcon from '@/assets/svg/YellowLockIcon';

export default function UnauthorizedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data } = useUserStore();

  const reason = searchParams.get('reason');
  const isSubscriptionExpired = reason === 'subscription_expired';

  // Get accountType from store or localStorage
  let accountTypeFromStorage = null;
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('subscriptionAccountType');
    if (stored) {
      accountTypeFromStorage = stored;
    }
  }
  const accountType = data?.accountType || accountTypeFromStorage;

  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleLogout = async () => {
    try {
      const { logout } = await import('@core/lib/auth/services');
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleUpgrade = () => {
    // Navigate to plan page with user data
    const upgradeData = {
      name: data?.name || '',
      email: data?.email || '',
    };
    // Store in localStorage using the correct key
    localStorage.setItem('subscription_user_data', JSON.stringify(upgradeData));
    router.push('/subscription/signup');
  };

  return (
    <div
      className="fixed inset-0 flex items-center justify-center p-6"
      style={{
        background:
          'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(194, 233, 254, 0.8) 100%)',
      }}
    >
      <div className="p-8 max-w-sm w-full flex flex-col justify-center align-center text-center bg-white rounded-lg shadow-lg">
        {isSubscriptionExpired ? (
          <>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br mb-4">
              <WarningIcon />
            </div>
            <h3 className="text-xl font-semibold text-[#001926] mb-2">
              Your plan expired
            </h3>
            <p className="text-[#001926] mb-4">
              {isMounted && accountType === 'clinic'
                ? 'Upgrade to continue'
                : 'Your organization plan expired, contact your admin to upgrade'}
            </p>
            <div className="flex flex-col  gap-2 max-w-md justify-center items-center">
              {isMounted && accountType === 'clinic' && (
                <>
                  <button
                    onClick={handleUpgrade}
                    className="px-4 py-1 text-md text-center w-full sm:w-auto bg-black rounded-3xl text-white "
                  >
                    <span>Upgrade now</span>
                  </button>
                  <span>or</span>
                </>
              )}

              <button
                onClick={handleLogout}
                className="px-8  text-xl font-medium w-full sm:w-auto bg-white text-[#E4626F] hover:text-red-700"
              >
                <span className="flex  flex-row text-center gap-2 justify-center align-center">
                  <SignoutIcon className="w-5 h-6" />
                  Sign out
                </span>
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br mb-3">
              <YellowLockIcon className="h-13 w-13" />
            </div>
            <h3 className="text-xl font-semibold text-[#001926] mb-2">
              Access Denied
            </h3>
            <p className="text-[#001926] mb-6">
              You don&apos;t have permission to access this application. Please
              contact your administrator if you believe this is an error.
            </p>
            <button
              onClick={handleLogout}
              className="px-8  text-xl font-medium w-full sm:w-auto bg-white text-[#E4626F] hover:text-red-700"
            >
              <span className="flex  flex-row text-center gap-2 justify-center align-center">
                <SignoutIcon className="w-5 h-6" />
                Sign out
              </span>
            </button>
          </>
        )}
      </div>
    </div>
  );
}
