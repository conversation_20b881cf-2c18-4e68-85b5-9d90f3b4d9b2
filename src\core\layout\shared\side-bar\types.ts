import { ReactNode } from 'react';

export type SidebarItem = {
  label: string;
  path: string;
  icon: ReactNode;
  disabled?: boolean;
  permissions?: string[];
  requireAll?: boolean; // If true, requires ALL permissions; if false or undefined, requires ANY permission
  department?: string[];
  module?: 'mrd' | 'emr';
  isLocked?: boolean;
  showLocked?: boolean;
};

export type SidebarProps = {
  items?: SidebarItem[];
  highlightColor?: string;
  renderBottom?: () => ReactNode;
};

export interface NavButtonProps {
  icon: ReactNode;
  text: string;
  href: string;
  disabled?: boolean;
  isLocked?: boolean;
  showLocked?: boolean;
  showAccessDenied?: boolean;
  highlightColor?: string;
}
