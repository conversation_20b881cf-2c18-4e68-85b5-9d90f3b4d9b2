'use client';

import { useState, useEffect } from 'react';

import { FaArrowLeft } from 'react-icons/fa';
import { toast } from 'sonner';

import { useRouter } from 'next/navigation';

import {
  getSubscriptionUserData,
  saveSubscriptionUserData,
  clearSubscriptionUserData,
} from '@/utils/subscription';

import { routes } from '@/constants/routes';

import AppSelect from '@/core/components/app-select';

interface EstablishmentOption {
  value: string;
  label: string;
}

const SelectEstablishmentView = () => {
  const router = useRouter();
  const [selectedEstablishment, setSelectedEstablishment] =
    useState<EstablishmentOption | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const establishmentOptions: EstablishmentOption[] = [
    { value: 'clinic', label: 'Clinic' },
    { value: 'hospital', label: 'Hospital' },
  ];

  useEffect(() => {
    // Check if user has completed signup
    const userData = getSubscriptionUserData();
    if (!userData || !userData.signupCompleted) {
      // Redirect back to signup if no user data found
      router.push(routes.SUBSCRIPTION_SIGNUP);
      return;
    }

    // Restore previously selected establishment if exists
    if (userData.establishment) {
      const savedEstablishment = establishmentOptions.find(
        (option) => option.value === userData.establishment
      );
      if (savedEstablishment) {
        setSelectedEstablishment(savedEstablishment);
      }
    }
  }, [router]);

  const handleEstablishmentChange = (option: EstablishmentOption | null) => {
    setSelectedEstablishment(option);
  };

  const handleBackNavigation = () => {
    // When going back from establishment to signup, clear all downstream data
    // This includes role and any plan selections since user is starting over
    clearSubscriptionUserData();
    router.push(routes.SUBSCRIPTION_SIGNUP);
  };

  const handleProceed = async () => {
    if (!selectedEstablishment) {
      toast.error('Please select an establishment type');
      return;
    }

    setIsLoading(true);

    try {
      // Store establishment selection
      saveSubscriptionUserData({
        establishment: selectedEstablishment.value,
      });

      // Navigate to role selection
      router.push(routes.SUBSCRIPTION_SELECT_ROLE);
    } catch (error) {
      console.error('Error saving establishment:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative"
      style={{
        background: 'linear-gradient(to bottom, #E6F6FF, #B4E5FE)',
      }}
    >
      {/* Back Arrow */}
      <div className="mb-6 absolute left-0 top-0 p-4">
        <button
          onClick={handleBackNavigation}
          className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
        >
          <FaArrowLeft />
        </button>
      </div>
      <div className="w-full max-w-md ">
        <div className="text-center mb-6">
          <h1
            className="text-gray-800"
            style={{ fontSize: '34px', fontWeight: 600 }}
          >
            Select the Establishment
          </h1>
        </div>

        <div className="mb-12">
          <div
            style={{
              backgroundColor: 'white',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
            }}
          >
            <AppSelect<EstablishmentOption>
              placeholder="Enterprise"
              options={establishmentOptions}
              value={selectedEstablishment}
              onChange={handleEstablishmentChange}
              getOptionValue={(option) => option.value}
              getOptionLabel={(option) => option.label}
              required
            />
          </div>
        </div>

        <button
          onClick={handleProceed}
          disabled={isLoading || !selectedEstablishment}
          className="w-full py-3 px-4 mt-20 bg-black hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
        >
          {isLoading ? 'Processing...' : 'Proceed'}
        </button>
      </div>
    </div>
  );
};

export default SelectEstablishmentView;
