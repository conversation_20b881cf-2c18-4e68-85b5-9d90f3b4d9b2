'use client';

import React, { memo, useMemo } from 'react';

import clsx from 'clsx';

import usePermission from '@/hooks/use-permission';

import { useUserStore } from '@/store/userStore';

import { shouldHideBasedOnSubscription } from '@/utils/subscription';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import NavLink from './NavLink';
import type { SidebarProps } from './types';

const Sidebar = ({
  items = [],
  highlightColor,
  renderBottom,
}: SidebarProps) => {
  const { hasDepartment } = usePermission();
  const { permissions = [], subscriptionFeatures, data } = useUserStore();

  const processedItems = useMemo(() => {
    // // Don't render items until user data is loaded to prevent showing locked state initially
    // if (
    //   permissions.length === 0 ||
    //   (data.subscriberId && !subscriptionFeatures)
    // ) {
    //   return [];
    // }

    return items
      .filter((item) => {
        if (item?.department && item.department.length > 0) {
          return hasDepartment(item.department);
        }
        return true;
      })
      .map((item) => {
        const hasModuleAccess = item.module
          ? permissions.includes(
              item.module === 'mrd'
                ? PERMISSION_KEYS.MRD_ACCESS
                : PERMISSION_KEYS.EMR_ACCESS
            )
          : true;
        const hasSpecificPermission = item.permissions
          ? item.requireAll
            ? item.permissions.every((perm) => permissions.includes(perm))
            : item.permissions.some((perm) => permissions.includes(perm))
          : true;
        const hasPermission = hasModuleAccess && hasSpecificPermission;
        const shouldHide = shouldHideBasedOnSubscription(
          permissions,
          subscriptionFeatures,
          data,
          item.permissions || [],
          item.module,
          item.requireAll || false
        );
        if (shouldHide) {
          return {
            ...item,
            showAccessDenied: true,
            isLocked: false,
            showLocked: false,
          };
        }
        return {
          ...item,
          showAccessDenied: false,
          isLocked: !hasPermission,
          showLocked: !hasPermission,
        };
      });
  }, [items, hasDepartment, permissions, subscriptionFeatures, data]);

  return (
    <div
      className={clsx(
        'flex flex-col flex-grow-0 flex-shrink-0',
        'bg-white rounded-base shadow-base',
        'h-full min-h-full w-17 min-w-17 max-w-17',
        'overflow-y-auto overflow-x-hidden'
      )}
    >
      <div
        className={clsx(
          'flex flex-col flex-shrink-0 flex-grow-0 items-center',
          'w-full min-w-full max-w-full'
        )}
      >
        {processedItems.map((item) => (
          <NavLink
            key={item.path}
            icon={item.icon}
            href={item.path}
            text={item?.label}
            disabled={item.disabled || false}
            isLocked={item.isLocked}
            showLocked={item.showLocked}
            showAccessDenied={item.showAccessDenied}
            highlightColor={highlightColor}
          />
        ))}
      </div>
      {renderBottom && renderBottom()}
    </div>
  );
};

export default memo(Sidebar);
