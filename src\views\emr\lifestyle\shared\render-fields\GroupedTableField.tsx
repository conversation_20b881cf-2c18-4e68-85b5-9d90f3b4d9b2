import React, { useCallback, useState, useEffect, useMemo } from 'react';

import {
  Controller,
  useFieldArray,
  useFormContext,
  useWatch,
} from 'react-hook-form';

import { Box, Typography, CircularProgress } from '@mui/material';

import { allowNumbersWithDecimals } from '@/utils/validation';

import AppIcon from '@/core/components/app-icon';
import AppIconButton from '@/core/components/app-icon-button';
import AppSelect from '@/core/components/app-select';
import AppTextField from '@/core/components/app-text-field';
import AppTimeRange from '@/core/components/app-time-range';
import DeleteModal from '@/core/components/delete-modal';
import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2 } from '@/core/components/table-v2/types';
import { api } from '@/core/lib/interceptor';
import { GroupedTableField as GroupedTableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

const foodSearchCache = new Map<string, { data: any[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000;
const MAX_CACHE_SIZE = 100;

const getCacheKey = (endpoint: string, query: string): string => {
  return `${endpoint}:${query.toLowerCase().trim()}`;
};

const cleanExpiredCache = () => {
  const now = Date.now();
  const entries = Array.from(foodSearchCache.entries());

  for (const [key, value] of entries) {
    if (now - value.timestamp > CACHE_DURATION) {
      foodSearchCache.delete(key);
    }
  }

  if (foodSearchCache.size > MAX_CACHE_SIZE) {
    const sortedEntries = Array.from(foodSearchCache.entries()).sort(
      (a, b) => a[1].timestamp - b[1].timestamp
    );
    const toRemove = sortedEntries.slice(
      0,
      foodSearchCache.size - MAX_CACHE_SIZE
    );
    toRemove.forEach(([key]) => foodSearchCache.delete(key));
  }
};

type DependentAutofillCellProps = {
  name: string;
  header: any;
  rowIndex: number;
  groupIndex: number;
  watchFn: (name: string) => any;
  setValueFn: (
    name: string,
    value: any,
    options?: { shouldValidate?: boolean }
  ) => void;
};

const DependentAutofillCellComponent: React.FC<DependentAutofillCellProps> = ({
  name,
  header,
  rowIndex,
  groupIndex,
  watchFn,
  setValueFn,
}) => {
  const dependsOnValue = watchFn(
    `${name}.${groupIndex}.rows.${rowIndex}.${header.dependsOn}`
  );
  const [isLoading, setIsLoading] = useState(false);
  const [fieldValue, setFieldValue] = useState('');

  useEffect(() => {
    const fetchDependentData = async () => {
      if (!dependsOnValue || !header.fetchDependentData) return;

      const {
        endpoint,
        method = 'GET',
        paramName = 'foodId',
        fieldMapping,
      } = header.fetchDependentData;

      try {
        setIsLoading(true);
        const response = await api({
          method,
          url: endpoint,
          params: {
            [paramName]: dependsOnValue,
          },
        });

        if (response.data) {
          const data = response.data.data || response.data;

          if (data.servings_unit) {
            const unitFieldName = `${name}.${groupIndex}.rows.${rowIndex}.servings_unit`;
            setValueFn(unitFieldName, data.servings_unit, {
              shouldValidate: true,
            });
            setFieldValue(data.servings_unit);
          }

          const fieldName = `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`;

          if (fieldMapping?.serving_type && data[fieldMapping.serving_type]) {
            const value = data[fieldMapping.serving_type];
            setValueFn(fieldName, value, { shouldValidate: true });
            setFieldValue(value);
          } else if (Object.values(data).length > 0) {
            const value = Object.values(data)[0] as string;
            if (typeof value === 'string') {
              setValueFn(fieldName, value, { shouldValidate: true });
              setFieldValue(value);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching dependent data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDependentData();
  }, [
    dependsOnValue,
    header.fetchDependentData,
    groupIndex,
    rowIndex,
    header.id,
    name,
    setValueFn,
  ]);

  return (
    <Box sx={{ width: '100%' }}>
      <AppTextField
        value={fieldValue || ''}
        fullWidth
        size="small"
        disabled
        placeholder={isLoading ? 'Loading...' : ''}
        InputProps={{
          readOnly: true,
          endAdornment: isLoading ? <CircularProgress size={20} /> : null,
          sx: {
            '& .MuiOutlinedInput-input': {
              backgroundColor: 'white',
              color: 'text.primary',
              cursor: 'default',
              opacity: 1,
            },
          },
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: 'divider',
            },
            '&:hover fieldset': {
              borderColor: 'divider',
            },
            '&.Mui-focused fieldset': {
              borderColor: 'divider',
            },
            backgroundColor: 'white',
            '&.Mui-disabled': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'divider !important',
              },
              '& .MuiInputBase-input': {
                color: 'text.primary',
                WebkitTextFillColor: 'text.primary',
              },
            },
          },
        }}
      />
    </Box>
  );
};

// Helper function to check if a field value is empty or unanswered
const isFieldEmpty = (value: any, field?: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return true;
  }
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true;
  }

  // Check for "No information found" or similar ambient listening fallback values
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim();
    if (
      lowerValue.includes('no information found') ||
      lowerValue.includes('not found') ||
      lowerValue.includes('no data') ||
      lowerValue.includes('unknown')
    ) {
      return true;
    }

    // If field has options, check if the value is not in the valid options
    if (field?.options && Array.isArray(field.options)) {
      const validOptions = field.options.map((opt: string) =>
        opt.toLowerCase().trim()
      );
      if (!validOptions.includes(lowerValue)) {
        return true;
      }
    }
  }

  return false;
};

// Convert 24-hour format (e.g., "13:00") to 12-hour format with AM/PM (e.g., "1:00 PM")
const convert24HourTo12Hour = (time24: string): string => {
  if (!time24 || typeof time24 !== 'string') return '';

  // If already in 12-hour format, normalize and return
  if (
    time24.toLowerCase().includes('am') ||
    time24.toLowerCase().includes('pm')
  ) {
    return normalizeTimeFormat(time24);
  }

  // Parse 24-hour format
  const [hourStr, minuteStr] = time24.split(':');
  if (!hourStr || !minuteStr) return '';

  const hour24 = parseInt(hourStr, 10);
  const minute = minuteStr.trim();

  if (isNaN(hour24) || hour24 < 0 || hour24 > 23) return '';

  // Convert to 12-hour format
  const period = hour24 >= 12 ? 'PM' : 'AM';
  const hour12 = hour24 % 12 || 12;

  return `${hour12}:${minute} ${period}`;
};

// Normalize time format: convert lowercase am/pm to uppercase AM/PM and ensure proper format
const normalizeTimeFormat = (time: string): string => {
  if (!time || typeof time !== 'string') return '';

  // Trim whitespace
  let normalized = time.trim();

  // Replace lowercase am/pm with uppercase AM/PM
  normalized = normalized.replace(/\bam\b/gi, 'AM').replace(/\bpm\b/gi, 'PM');

  // Handle formats like "1 pm" or "1pm" - add ":00" if minutes are missing
  // Match pattern: number (optional space) AM/PM
  const timePattern = /^(\d{1,2})\s*(AM|PM)$/i;
  const match = normalized.match(timePattern);

  if (match) {
    const hour = match[1];
    const period = match[2].toUpperCase();
    // Add ":00" for minutes
    normalized = `${hour}:00 ${period}`;
  }

  // Handle formats like "1: pm" or "1:pm" - add "00" if minutes are missing
  const timePatternWithColon = /^(\d{1,2}):\s*(AM|PM)$/i;
  const matchWithColon = normalized.match(timePatternWithColon);

  if (matchWithColon) {
    const hour = matchWithColon[1];
    const period = matchWithColon[2].toUpperCase();
    normalized = `${hour}:00 ${period}`;
  }

  return normalized;
};

export const GroupedTableField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
  isAmbientForm,
}) => {
  const tableField = field as GroupedTableFieldType;
  const [hasInitialized, setHasInitialized] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<{
    groupIndex: number;
    rowIndex: number;
    rowData: any;
  } | null>(null);

  const { fields, append } = useFieldArray({
    control,
    name: name,
  });

  const { setValue, watch } = useFormContext();
  const watchedGroups = useWatch({
    control,
    name,
  }) as any[] | undefined;

  const mealGroups = useMemo(
    () => tableField.mealGroups || [],
    [tableField.mealGroups]
  );

  const normalizeGroups = useCallback(
    (source?: any[]) => {
      const base =
        Array.isArray(source) && source.length > 0
          ? source
          : (fields as unknown as any[]);

      return mealGroups.map((mealGroup, index) => {
        const group = base?.[index] ?? {};
        const fallback = (fields as unknown as any[])[index] ?? {};
        const rowsSource =
          group?.rows ?? fallback?.rows ?? mealGroup?.defaultRows ?? [];

        // Always start with empty defaultTime - no default values
        const defaultTimeValue = {
          from: '',
          to: '',
        };

        const rows = Array.isArray(rowsSource)
          ? rowsSource.map((row: any) => {
              const rowTime = row?.time_range || row?.timings || {};

              // Handle time_range as string (e.g., "13:00-14:00" from API)
              let fromTime = '';
              let toTime = '';

              if (typeof rowTime === 'string' && rowTime.includes('-')) {
                // Parse string format like "13:00-14:00"
                const [from, to] = rowTime.split('-');
                if (from && to) {
                  // Convert 24-hour format to 12-hour format with AM/PM
                  fromTime = convert24HourTo12Hour(from.trim());
                  toTime = convert24HourTo12Hour(to.trim());
                }
              } else if (rowTime && typeof rowTime === 'object') {
                // Handle object format { from: string, to: string }
                fromTime = rowTime?.from || '';
                toTime = rowTime?.to || '';

                // Normalize AM/PM to uppercase (API might return lowercase "pm" or "am")
                if (fromTime) {
                  fromTime = normalizeTimeFormat(fromTime);
                }
                if (toTime) {
                  toTime = normalizeTimeFormat(toTime);
                }
              }

              return {
                ...row,
                time_range: {
                  from: fromTime,
                  to: toTime,
                },
              };
            })
          : [];

        return {
          id: group?.id ?? fallback?.id ?? mealGroup?.id,
          label: group?.label ?? fallback?.label ?? mealGroup?.label,
          rows,
          defaultTime: defaultTimeValue,
        };
      });
    },
    [fields, mealGroups]
  );

  const [uiGroups, setUiGroups] = useState(() =>
    normalizeGroups(fields as any[])
  );

  useEffect(() => {
    const normalized = normalizeGroups(watchedGroups);
    setUiGroups(normalized);

    if (JSON.stringify(normalized) !== JSON.stringify(watchedGroups || [])) {
      setTimeout(() => {
        setValue(name, normalized as any, {
          shouldValidate: false,
          shouldDirty: false,
        });
      }, 0);
    }
  }, [watchedGroups, normalizeGroups, setValue, name]);

  const addRow = useCallback(
    (groupIndex: number) => {
      // Always start with empty time_range for new rows in both create and edit modes
      const newRow: Record<string, any> = {
        time_range: {
          from: '',
          to: '',
        },
      };

      // Initialize all other fields based on headers
      tableField.headers.forEach((header) => {
        // Skip time_range as it's already set above
        if (header.type === 'time_range') {
          // Already set above, but ensure header.id is also set if different
          if (header.id !== 'time_range') {
            newRow[header.id] = {
              from: '',
              to: '',
            };
          }
        } else if (header.type === 'select') {
          newRow[header.id] = '';
        } else {
          newRow[header.id] = '';
        }
      });

      const updatedGroups = uiGroups.map((group, index) => {
        if (index !== groupIndex) return group;

        // Keep existing rows as they are - don't apply defaults
        const rowsWithDefaults = (group.rows || []).map((row: any) => {
          const currentRowTime = row?.time_range || row?.timings || {};

          return {
            ...row,
            time_range: {
              from: currentRowTime?.from || '',
              to: currentRowTime?.to || '',
            },
          };
        });

        return {
          ...group,
          rows: [
            ...rowsWithDefaults,
            {
              ...newRow,
              // Explicitly ensure time_range is empty
              time_range: {
                from: '',
                to: '',
              },
            },
          ],
          defaultTime: { from: '', to: '' },
        };
      });

      setUiGroups(updatedGroups);
      setValue(name, updatedGroups, {
        shouldValidate: true,
        shouldDirty: true,
      });

      // Explicitly set the time_range field to empty for the new row to ensure it's properly initialized
      const newRowIndex = updatedGroups[groupIndex].rows.length - 1;
      const timeRangeFieldName = `${name}.${groupIndex}.rows.${newRowIndex}.time_range`;
      setValue(
        timeRangeFieldName as any,
        { from: '', to: '' },
        {
          shouldValidate: false,
          shouldDirty: false,
        }
      );
    },
    [uiGroups, tableField.headers, name, setValue, setUiGroups]
  );

  const handleDeleteClick = useCallback(
    (groupIndex: number, rowIndex: number) => {
      const group = uiGroups[groupIndex] as any;
      const rowData = group?.rows?.[rowIndex];
      setRowToDelete({ groupIndex, rowIndex, rowData });
      setDeleteModalOpen(true);
    },
    [uiGroups]
  );

  const handleConfirmDelete = useCallback(() => {
    if (rowToDelete !== null) {
      const updatedGroups = uiGroups.map((group, index) => {
        if (index !== rowToDelete.groupIndex) {
          return group;
        }

        // Keep existing rows as they are - don't apply defaults
        const normalizedRows = (group.rows || []).map((row: any) => {
          const currentRowTime = row?.time_range || row?.timings || {};

          return {
            ...row,
            time_range: {
              from: currentRowTime?.from || '',
              to: currentRowTime?.to || '',
            },
          };
        });

        const filteredRows = normalizedRows.filter(
          (_: any, rowIndex: number) => rowIndex !== rowToDelete.rowIndex
        );

        return {
          ...group,
          rows: filteredRows,
          defaultTime: { from: '', to: '' },
        };
      });

      setUiGroups(updatedGroups);
      setValue(name, updatedGroups, {
        shouldValidate: true,
        shouldDirty: true,
      });
      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  }, [rowToDelete, uiGroups, name, setValue, setUiGroups]);

  const handleCancelDelete = useCallback(() => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  }, []);

  const getRowDisplayName = useCallback(() => {
    if (!rowToDelete?.rowData) return 'this entry';
    return rowToDelete.rowData.food_item || 'this entry';
  }, [rowToDelete]);

  // Updated SearchableSelectCell component for GroupedTableField
  const SearchableSelectCell = ({ name, header, control, disabled }: any) => {
    const [searchQuery, setSearchQuery] = React.useState('');
    const [menuIsOpen, setMenuIsOpen] = React.useState(false);
    const [hasFetchedOnOpen, setHasFetchedOnOpen] = React.useState(false);

    const fetchOptions = React.useCallback(
      async (inputValue: string) => {
        if (!header.fetchOptions) return [];

        const {
          endpoint,
          method = 'GET',
          queryParam = 'input',
        } = header.fetchOptions;

        // Clean expired cache entries periodically
        cleanExpiredCache();

        // Check cache first - each unique search query has its own cache entry
        // This ensures that typing "a", then "ab", then "a" again will show correct results
        const searchQuery = inputValue.trim();
        if (searchQuery) {
          const cacheKey = getCacheKey(endpoint, searchQuery);
          const cached = foodSearchCache.get(cacheKey);

          if (cached) {
            const now = Date.now();

            if (now - cached.timestamp < CACHE_DURATION) {
              return cached.data;
            } else {
              foodSearchCache.delete(cacheKey);
            }
          }
        }

        try {
          const params: Record<string, string> = {};

          if (searchQuery.trim()) {
            params[queryParam] = searchQuery.trim();
          }

          const response = await api({
            method,
            url: endpoint,
            params: Object.keys(params).length > 0 ? params : undefined,
          });

          const items = response.data?.data || response.data || [];
          const foodItems = Array.isArray(items) ? items : [items];

          const formattedOptions = foodItems.map((item: any) => ({
            value: item.food_name || item.value || item.label || '',
            label: item.food_name || item.label || item.value || '',
          }));

          const cacheKey = getCacheKey(endpoint, searchQuery.trim() || '');
          foodSearchCache.set(cacheKey, {
            data: formattedOptions,
            timestamp: Date.now(),
          });

          return formattedOptions;
        } catch (error) {
          console.error('Error fetching options:', error);
          return [];
        }
      },
      [header.fetchOptions]
    );

    const [options, setOptions] = React.useState<any[]>([]);
    const [isLoading, setIsLoading] = React.useState(false);
    const fieldValue = useWatch({
      control,
      name,
    });

    const resolvedOption = React.useMemo(() => {
      if (
        fieldValue === undefined ||
        fieldValue === null ||
        (typeof fieldValue === 'string' && fieldValue === '')
      ) {
        return null;
      }

      const normalizedValue = String(fieldValue);
      const match = options.find((opt: any) => {
        const candidateValue: string = String(opt?.value ?? opt?.label ?? '');
        return candidateValue === normalizedValue;
      });

      if (match) return match;

      return {
        value: normalizedValue,
        label: normalizedValue,
      };
    }, [fieldValue, options]);

    React.useEffect(() => {
      let active = true;

      const doFetch = async () => {
        if (menuIsOpen && !hasFetchedOnOpen && !searchQuery.trim()) {
          setIsLoading(true);
          try {
            const result = await fetchOptions('');
            if (active) {
              setOptions(result || []);
              setHasFetchedOnOpen(true);
            }
          } catch (err) {
            console.error(
              'SearchableSelectCell fetch error on menu open:',
              err
            );
            if (active) setOptions([]);
          } finally {
            if (active) setIsLoading(false);
          }
          return;
        }

        if (searchQuery.trim()) {
          setIsLoading(true);
          try {
            const result = await fetchOptions(searchQuery);
            if (active) setOptions(result || []);
          } catch (err) {
            console.error('SearchableSelectCell fetch error:', err);
            if (active) setOptions([]);
          } finally {
            if (active) setIsLoading(false);
          }
          return;
        }

        if (!menuIsOpen && !searchQuery.trim()) {
          if (active) {
            setOptions([]);
            setIsLoading(false);
          }
        }
      };

      doFetch();

      return () => {
        active = false;
      };
    }, [searchQuery, menuIsOpen, hasFetchedOnOpen, name, fetchOptions]); // Use 'name' instead of 'header.id' to scope to specific field

    const LoadingMessage = () => null;
    const LoadingIndicator = () => null;

    const customComponents = {
      IndicatorSeparator: null,
      LoadingMessage,
      LoadingIndicator,
    };

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value } }) => {
          const currentOption = (() => {
            if (value !== fieldValue) {
              if (
                value === undefined ||
                value === null ||
                (typeof value === 'string' && value === '')
              ) {
                return null;
              }
              const normalizedValue = String(value);
              const match = options.find((opt: any) => {
                const candidateValue: string = String(
                  opt?.value ?? opt?.label ?? ''
                );
                return candidateValue === normalizedValue;
              });
              return (
                match || {
                  value: normalizedValue,
                  label: normalizedValue,
                }
              );
            }
            return resolvedOption;
          })();

          return (
            <Box
              sx={{ width: '100%', backgroundColor: 'white', borderRadius: 1 }}
            >
              <AppSelect
                value={currentOption}
                onChange={(selectedOption: any) => {
                  // Don't allow selection of loading option
                  if (selectedOption?.value === '__loading__') {
                    return;
                  }
                  onChange(selectedOption?.value || '');
                  setMenuIsOpen(false); // Close menu after selection
                }}
                onMenuOpen={() => {
                  setMenuIsOpen(true);
                  setHasFetchedOnOpen(false);
                }}
                onMenuClose={() => {
                  setMenuIsOpen(false);
                  setHasFetchedOnOpen(false);
                }}
                onInputChange={(newValue) => {
                  setSearchQuery(newValue);
                }}
                options={
                  isLoading
                    ? [
                        {
                          value: '__loading__',
                          label: 'Loading...',
                          isDisabled: true,
                        },
                      ]
                    : options
                }
                placeholder={header.placeholder || 'Search...'}
                isLoading={false}
                isDisabled={disabled}
                isClearable
                isSearchable
                showSearchIcon={true} // Enable search icon
                menuIsOpen={menuIsOpen}
                menuPosition="fixed"
                menuPortalTarget={
                  (document.getElementById('app-modal') as HTMLElement) ||
                  document.body
                }
                styles={{
                  control: (base, state) => ({
                    ...base,
                    width: '100%',
                    minWidth: '180px',
                    minHeight: '36px',
                    backgroundColor: 'white',
                    borderColor: state.isFocused ? 'primary.main' : 'grey.300',
                    boxShadow: state.isFocused
                      ? '0 0 0 1px primary.main'
                      : 'none',
                    '&:hover': {
                      borderColor: 'primary.main',
                    },
                  }),
                  valueContainer: (base) => ({
                    ...base,
                    padding: '2px 8px',
                  }),
                  input: (base) => ({
                    ...base,
                    margin: 0,
                    padding: '8px 2px',
                  }),
                  singleValue: (base) => ({
                    ...base,
                    maxWidth: 'calc(100% - 32px)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }),
                  menu: (base) => ({
                    ...base,
                    zIndex: 9999,
                    marginTop: '4px',
                    boxShadow:
                      '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  }),
                  option: (base, state) => {
                    const isDisabled = state.data?.value === '__loading__';
                    return {
                      ...base,
                      padding: '8px 12px',
                      backgroundColor: state.isSelected
                        ? 'primary.light'
                        : 'white',
                      color: isDisabled
                        ? 'grey.500'
                        : state.isSelected
                          ? 'white'
                          : 'text.primary',
                      cursor: isDisabled ? 'default' : 'pointer',
                      '&:hover': {
                        backgroundColor: isDisabled ? 'white' : 'grey.100',
                      },
                    };
                  },
                }}
                components={customComponents}
              />
            </Box>
          );
        }}
      />
    );
  };

  const { headers, rows } = useMemo(() => {
    const filteredHeaders = readonly
      ? tableField.headers.filter((header) => header.type !== 'icon')
      : tableField.headers;

    const tableHeaders: HeaderV2[] = readonly
      ? [
          {
            key: 'meal',
            header: 'Meal',
            cellProps: { align: 'center' as const },
          },
          ...filteredHeaders.map((header) => ({
            key: header.id,
            header: header.label,
            cellProps: { align: 'center' as const },
          })),
        ]
      : tableField.headers.map((header) => ({
          key: header.id,
          header: header.label,
          cellProps: { align: 'center' as const },
        }));

    const tableRows: RowV2[] = [];

    if (readonly) {
      uiGroups.forEach((group: any, groupIndex) => {
        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableRow['meal'] = {
              value: <Typography variant="body2">{group.label}</Typography>,
              cellProps: { align: 'center' as const },
            };

            filteredHeaders.forEach((header) => {
              const cellValue = watch(
                `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`
              );

              const isEmpty =
                isAmbientForm &&
                header.type !== 'icon' &&
                (header.type === 'time_range'
                  ? !cellValue || (cellValue.from === '' && cellValue.to === '')
                  : isFieldEmpty(cellValue, header));

              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      if (readonly) {
                        if (header.type === 'time_range') {
                          const timeValue = controllerField.value;
                          const fromTime = timeValue?.from || '';
                          const toTime = timeValue?.to || '';

                          // Show time if either from or to is available
                          let displayTime = '-';
                          if (fromTime && toTime) {
                            displayTime = `${fromTime} - ${toTime}`;
                          } else if (fromTime) {
                            displayTime = fromTime;
                          } else if (toTime) {
                            displayTime = toTime;
                          }

                          return (
                            <Typography variant="body2">
                              {displayTime}
                            </Typography>
                          );
                        }
                        if (
                          header.type === 'select' ||
                          header.type === 'searchable_select' ||
                          header.type === 'dependent_autofill'
                        ) {
                          type OptionType =
                            | string
                            | { value: string; label: string };
                          const value = controllerField.value as string;
                          const options = (header.options ||
                            []) as OptionType[];

                          if (!options.length) {
                            return (
                              <Typography variant="body2">
                                {value || '-'}
                              </Typography>
                            );
                          }

                          const option = options.find((opt) =>
                            typeof opt === 'string'
                              ? opt === value
                              : opt.value === value
                          );

                          const displayValue =
                            option === undefined
                              ? value
                              : typeof option === 'string'
                                ? option
                                : option.label;

                          return (
                            <Typography variant="body2">
                              {displayValue || value || '-'}
                            </Typography>
                          );
                        }
                        return (
                          <Typography variant="body2">
                            {controllerField.value || '-'}
                          </Typography>
                        );
                      }

                      // Editable mode - show form controls
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'searchable_select') {
                        const fieldName = `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`;
                        return (
                          <SearchableSelectCell
                            key={`searchable-${groupIndex}-${rowIndex}-${header.id}`}
                            name={fieldName}
                            header={header}
                            control={control}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'dependent_autofill') {
                        const existingValue = controllerField.value;
                        // If value exists, display it as read-only text
                        if (existingValue && existingValue !== '') {
                          return (
                            <Typography variant="body2">
                              {existingValue}
                            </Typography>
                          );
                        }

                        return (
                          <DependentAutofillCellComponent
                            name={name}
                            header={header}
                            rowIndex={rowIndex}
                            groupIndex={groupIndex}
                            watchFn={watch}
                            setValueFn={setValue}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        const currentValue = controllerField.value || '';

                        const baseOptions = Array.isArray(header.options)
                          ? header.options.map((option) => ({
                              label: option,
                              value: option,
                            }))
                          : [];

                        const options =
                          currentValue &&
                          !baseOptions.find((opt) => opt.value === currentValue)
                            ? [
                                ...baseOptions,
                                { label: currentValue, value: currentValue },
                              ]
                            : baseOptions;

                        return (
                          <AppSelect
                            {...controllerField}
                            value={currentValue}
                            options={options}
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            type="number"
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: { min: header.min || 0 },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white !important',
                              },
                            }}
                          />
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: {
                  align: 'center' as const,
                  className: isEmpty ? 'ambient-empty-cell' : '',
                  sx: isEmpty
                    ? {
                        borderTop: '1px solid #d1d5db',
                        borderLeft: '1px solid #d1d5db',
                        borderRight: '1px solid #d1d5db',
                        borderBottom: '1px solid #d1d5db',
                        boxShadow: 'inset 0 0 0 1px #dc2626',
                      }
                    : {},
                },
              };
            });

            tableRows.push(tableRow);
          });
        }
      });
    } else {
      // Edit mode: Show group headers with add buttons
      uiGroups.forEach((group: any, groupIndex) => {
        const groupHeaderRow: RowV2 = {
          key: `group-${group.id}`,
        };

        tableField.headers.forEach((header, index) => {
          if (index === 0) {
            groupHeaderRow[header.id] = {
              value: (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                  }}
                >
                  <Typography
                    variant="h6"
                    fontWeight="medium"
                    sx={{
                      color:
                        isAmbientForm &&
                        (!group.rows ||
                          group.rows.length === 0 ||
                          group.rows.every((row: any) =>
                            Object.values(row).every((value: any) =>
                              isFieldEmpty(value)
                            )
                          ))
                          ? '#FF742A'
                          : 'inherit',
                    }}
                  >
                    {group.label}
                  </Typography>
                  <AppIconButton
                    onClick={() => addRow(groupIndex)}
                    variant="outlined"
                    color="info"
                    sx={{ borderRadius: 1, px: 2 }}
                  >
                    <AppIcon icon="humbleicons:plus" />
                  </AppIconButton>
                </Box>
              ),
              cellProps: {
                colSpan: tableField.headers.length,
                sx: {
                  backgroundColor: '#f9fafb',
                  borderBottom: '2px solid #e5e7eb',
                  py: '5px !important',
                },
              },
            };
          } else {
            groupHeaderRow[header.id] = {
              value: null,
              returnNullForEmpty: true,
            };
          }
        });

        tableRows.push(groupHeaderRow);

        if (group.rows && group.rows.length > 0) {
          group.rows.forEach((_: any, rowIndex: number) => {
            const tableRow: RowV2 = {
              key: `${group.id}-${rowIndex}`,
            };

            tableField.headers.forEach((header) => {
              // Get cell value to check if empty for ambient form highlighting
              // Exclude icon type cells (close/delete button) from highlighting
              const cellValue = watch(
                `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`
              );
              // For time_range, check if both from and to are empty
              // Don't highlight icon type cells
              const isEmpty =
                isAmbientForm &&
                header.type !== 'icon' &&
                (header.type === 'time_range'
                  ? !cellValue || (cellValue.from === '' && cellValue.to === '')
                  : isFieldEmpty(cellValue, header));

              tableRow[header.id] = {
                value: (
                  <Controller
                    name={`${name}.${groupIndex}.rows.${rowIndex}.${header.id}`}
                    control={control}
                    defaultValue={
                      header.type === 'time_range' ? { from: '', to: '' } : ''
                    }
                    render={({ field: controllerField }) => {
                      if (header.type === 'time_range') {
                        return (
                          <AppTimeRange
                            value={controllerField.value}
                            onChange={controllerField.onChange}
                            disabled={readonly}
                            slotProps={{
                              select: {
                                menuPosition: 'fixed',
                                menuPortalTarget:
                                  (document.getElementById(
                                    'app-modal'
                                  ) as HTMLElement) || document.body,
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'searchable_select') {
                        const fieldName = `${name}.${groupIndex}.rows.${rowIndex}.${header.id}`;
                        return (
                          <SearchableSelectCell
                            key={`searchable-${groupIndex}-${rowIndex}-${header.id}`}
                            name={fieldName}
                            header={header}
                            control={control}
                            disabled={readonly}
                          />
                        );
                      } else if (header.type === 'dependent_autofill') {
                        const existingValue = controllerField.value;

                        if (existingValue && existingValue !== '') {
                          return (
                            <Typography variant="body2">
                              {existingValue}
                            </Typography>
                          );
                        }

                        return (
                          <DependentAutofillCellComponent
                            name={name}
                            header={header}
                            rowIndex={rowIndex}
                            groupIndex={groupIndex}
                            watchFn={watch}
                            setValueFn={setValue}
                          />
                        );
                      } else if (header.type === 'select' && header.options) {
                        const currentValue = controllerField.value || '';
                        // Create options from header.options
                        const baseOptions = Array.isArray(header.options)
                          ? header.options.map((option: string) => ({
                              label: option,
                              value: option,
                            }))
                          : [];

                        const options =
                          currentValue &&
                          !baseOptions.find((opt) => opt.value === currentValue)
                            ? [
                                ...baseOptions,
                                { label: currentValue, value: currentValue },
                              ]
                            : baseOptions;

                        return (
                          <AppSelect
                            {...controllerField}
                            value={currentValue}
                            options={options}
                            isDisabled={readonly}
                            formControlProps={{ sx: { minWidth: 200 } }}
                            menuPosition="fixed"
                            menuPortalTarget={
                              (document.getElementById(
                                'app-modal'
                              ) as HTMLElement) || document.body
                            }
                            id={`group-${group.id}-row-${rowIndex}-field-${header.id}`}
                          />
                        );
                      } else if (header.type === 'number') {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            slotProps={{
                              htmlInput: {
                                min: header.min || 0,
                                onKeyDown: allowNumbersWithDecimals,
                              },
                            }}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      } else if (header.type === 'icon') {
                        return (
                          <div>
                            <AppIconButton
                              onClick={() =>
                                handleDeleteClick(groupIndex, rowIndex)
                              }
                              variant="outlined"
                              color="error"
                            >
                              <AppIcon icon="ic:round-close" />
                            </AppIconButton>
                          </div>
                        );
                      } else {
                        return (
                          <AppTextField
                            {...controllerField}
                            value={controllerField.value || ''}
                            size="small"
                            fullWidth
                            disabled={readonly}
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: readonly
                                  ? '#f9fafb !important'
                                  : 'white !important',
                              },
                            }}
                          />
                        );
                      }
                    }}
                  />
                ),
                cellProps: {
                  align: 'center' as const,
                  className: isEmpty ? 'ambient-empty-cell' : '',
                  sx: isEmpty
                    ? {
                        borderTop: '1px solid #d1d5db',
                        borderLeft: '1px solid #d1d5db',
                        borderRight: '1px solid #d1d5db',
                        borderBottom: '1px solid #d1d5db',
                        boxShadow: 'inset 0 0 0 1px #dc2626',
                      }
                    : {},
                },
              };
            });

            tableRows.push(tableRow);
          });
        } else {
          tableRows.push({
            key: `${group.id}-empty`,
            ...tableField.headers.reduce(
              (acc, header, index) => {
                if (index === 0) {
                  acc[header.id] = {
                    value: (
                      <Typography
                        variant="body2"
                        sx={{
                          fontStyle: 'italic',
                          color: isAmbientForm ? '#FF742A' : 'text.secondary',
                        }}
                      >
                        No entries for {group.label}
                      </Typography>
                    ),
                    cellProps: {
                      colSpan: tableField.headers.length,
                      align: 'center' as const,
                      sx: { py: 2 },
                    },
                  };
                } else {
                  acc[header.id] = {
                    value: null,
                    returnNullForEmpty: true,
                  };
                }
                return acc;
              },
              {} as Record<string, any>
            ),
          });
        }
      });
    }

    return { headers: tableHeaders, rows: tableRows };
  }, [
    uiGroups,
    tableField.headers,
    readonly,
    addRow,
    handleDeleteClick,
    isAmbientForm,
    control,
    name,
    setValue,
    watch,
  ]);

  useEffect(() => {
    if (!hasInitialized) {
      if (fields.length === 0 && tableField.mealGroups) {
        tableField.mealGroups.forEach((group) => {
          append({
            id: group.id,
            label: group.label,
            rows: group.defaultRows || [],
          });
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasInitialized, tableField.mealGroups, append]);

  return (
    <div className="space-y-6">
      <TableV2
        headers={headers}
        rows={rows}
        tableContainerProps={{
          sx: {
            '& .MuiTableCell-root': {
              padding: '12px 16px',
            },
            '& thead th': {
              fontSize: '14px',
              fontWeight: 600, // semi-bold
            },
            '& tbody tr': {
              backgroundColor: 'white !important',
            },
            ...(readonly && {
              '& .MuiTableHead-root .MuiTableCell-root': {
                backgroundColor: '#64707D',
              },
            }),
          },
        }}
        noDataMessage={
          <Typography variant="body2" color="text.secondary">
            No data available
          </Typography>
        }
      />

      <DeleteModal
        open={deleteModalOpen}
        onClose={handleCancelDelete}
        onDelete={handleConfirmDelete}
        confirmationMessage={`Are you sure you want to delete ${getRowDisplayName()}?`}
      />
    </div>
  );
};
