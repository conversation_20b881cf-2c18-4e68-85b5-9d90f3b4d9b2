import React from 'react';

import { Tooltip as MuiTooltip } from '@mui/material';

interface FeatureTooltipProps {
  children: React.ReactElement;
  title: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
}

const FeatureTooltip: React.FC<FeatureTooltipProps> = ({
  children,
  title,
  placement = 'auto',
}) => {
  return (
    <MuiTooltip
      title={
        <div
          style={{
            color: 'black',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {title}
        </div>
      }
      placement={placement}
      arrow
      slotProps={{
        tooltip: {
          sx: {
            fontSize: '14px',
            minHeight: '40px',
            minWidth: '140px',
            textAlign: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            color: 'black',
            border: '1px solid #ccc',
          },
        },
      }}
    >
      {children}
    </MuiTooltip>
  );
};

export default FeatureTooltip;
