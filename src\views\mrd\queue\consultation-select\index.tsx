import { useEffect } from 'react';

import Loading from '@/lib/common/loading';

import { useDoctorStore as usePersonalDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { fetchAllDoctors, useDoctorStore } from '@/store/mrd/queue/doctor';
import { useUserStore } from '@/store/userStore';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const ConsultationSelect = () => {
  const { doctors, setSelectedDoctor, selectedDoctor } = useDoctorStore();
  const { data: userData } = useUserStore();
  const { doctorProfile } = usePersonalDoctorStore();

  // Ensure value is always controlled and updated immediately
  const handleChangeDoctor = (id: string) => {
    const selected = doctors.find((item) => item.id === id) || null;
    setSelectedDoctor(selected);
  };

  const handleOpenChange = async (open: boolean) => {
    if (open && !doctors.length) {
      await fetchAllDoctors();
    }
  };

  const isClinician = userData?.accountType === 'clinic';
  const currentUserDoctor = isClinician
    ? {
        id: userData.id,
        name: doctorProfile?.general?.fullName || userData?.name || 'Unknown',
      }
    : null;
  const defaultValue = selectedDoctor?.id || currentUserDoctor?.id || '';

  useEffect(() => {
    if (!selectedDoctor && currentUserDoctor) {
      setSelectedDoctor(currentUserDoctor);
    }
  }, [selectedDoctor, currentUserDoctor, setSelectedDoctor]);

  return (
    <div>
      <Select
        value={defaultValue}
        onValueChange={handleChangeDoctor}
        onOpenChange={handleOpenChange}
        disabled={isClinician}
      >
        <SelectTrigger
          className={`flex gap-5 items-center justify-between border border-black px-1 py-1 rounded-full h-6 text-xs ${isClinician ? 'text-gray-900' : ''}`}
        >
          <SelectValue placeholder="Select a Doctor">
            {selectedDoctor?.name}
          </SelectValue>
        </SelectTrigger>

        <SelectContent
          position="popper"
          align="end"
          sideOffset={8}
          defaultValue={''}
          className="overflow-hidden border border-black bg-[#FCFCFC] rounded-lg w-full flex-1"
        >
          {!doctors.length && <Loading />}

          {doctors.map((item) => (
            <SelectItem
              key={item.id}
              value={item.id}
              className="data-[state=checked]:text-[#1AA6F1] hover:cursor-pointer outline-none"
            >
              {item.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ConsultationSelect;
