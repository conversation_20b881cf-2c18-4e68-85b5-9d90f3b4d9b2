import { toast } from 'sonner';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import {
  getOrganizationPlan,
  getSubscriptionPlans,
  getSubscriber,
  submitQuoteRequest,
  subscribeAfterPayment,
  calculateProratedAmount,
  startTrial,
  changePlanSubscription,
  validateEmail,
} from '@/query/subscription';

import { getErrorMessage } from '@/utils/error-message';
import {
  OrganizationPlan,
  SubscriptionPlansResponse,
  QuoteRequest,
  Feature,
  saveSubscriptionUserData,
  getSubscriptionUserData,
} from '@/utils/subscription';

type BillingCycle = 'monthly' | 'yearly';

interface SubscriptionState {
  // Organization Plan
  organizationPlan: OrganizationPlan | null;
  isLoadingOrganizationPlan: boolean;

  // Subscription Plans
  subscriptionPlans: SubscriptionPlansResponse | null;
  isLoadingPlans: boolean;

  // Subscriber Data
  subscriberData: any | null;
  isLoadingSubscriber: boolean;

  // UI State
  selectedPlan: string | null;
  billingCycle: BillingCycle;
  selectedAddOnFeatures: Feature[];

  // Quote Request
  isSubmittingQuote: boolean;

  // Subscription
  isSubscribing: boolean;

  // Prorated Calculation
  proratedCalculation: {
    proratedAmount: number;
    currentPlanRefund: number;
    newPlanAmount: number;
    finalAmount: number;
  } | null;
  isCalculatingProrated: boolean;

  // Modals
  showUpgradeModal: boolean;
  showAddFeaturesModal: boolean;
  showQuoteModal: boolean;
  showSuccessModal: boolean;
  showFailureModal: boolean;
}

interface SubscriptionActions {
  // Organization Plan Actions
  fetchOrganizationPlan: () => Promise<void>;

  // Subscription Plans Actions
  fetchSubscriptionPlans: (includeInactive?: boolean) => Promise<void>;

  // Subscriber Actions
  fetchSubscriber: (subscriberId: string) => Promise<void>;

  // Email Validation
  validateUserEmail: (email: string) => Promise<{
    isValid: boolean;
    canProceed: boolean;
    message: string;
  }>;

  // UI Actions
  setSelectedPlan: (planId: string | null) => void;
  setBillingCycle: (cycle: BillingCycle) => void;
  setSelectedAddOnFeatures: (features: Feature[]) => void;
  toggleAddOnFeature: (feature: Feature) => void;

  // Quote Actions
  submitQuote: (data: QuoteRequest) => Promise<boolean>;

  // Subscription Actions
  subscribe: (
    planId: string,
    billingPeriod: BillingCycle,
    billingDetails: {
      name: string;
      email: string;
      phoneNumber?: string;
      pincode?: string;
    },
    selectedAddOnFeatures: Feature[],
    paymentId: string
  ) => Promise<boolean>;

  subscribePaidPlan: (
    planId: string,
    billingPeriod: BillingCycle,
    billingDetails: {
      name: string;
      email: string;
      phoneNumber?: string;
      pincode?: string;
    },
    selectedAddOnFeatures: Feature[],
    paymentId: string
  ) => Promise<boolean>;

  // Change Plan Actions (for profile flows)
  changePlanSubscription: (data: {
    email: string;
    newPlanId: string;
    billingType: 'monthly' | 'yearly';
    selectedAddOnFeatures: {
      MRD: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      EMR: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      Billing: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
    };
    paymentId: string;
  }) => Promise<boolean>;

  // Trial Actions
  startTrial: (data: {
    email: string;
    name: string;
    phoneNumber?: string | null;
    planId: string;
    billingType: 'monthly' | 'yearly';
    roleId: string;
    userType: string;
    userRole: string;
    Billing: Array<{
      name: string;
      email: string;
      pincode: string;
    }>;
  }) => Promise<boolean>;

  // Prorated Calculation Actions
  calculateProrated: (data: {
    currentPlanId: string;
    newPlanId: string;
    billingPeriod: BillingCycle;
    currentPlanStartDate: string;
  }) => Promise<void>;

  // Modal Actions
  setShowUpgradeModal: (show: boolean) => void;
  setShowAddFeaturesModal: (show: boolean) => void;
  setShowQuoteModal: (show: boolean) => void;
  setShowSuccessModal: (show: boolean) => void;
  setShowFailureModal: (show: boolean) => void;

  // Reset Actions
  resetState: () => void;
}

type SubscriptionStore = SubscriptionState & SubscriptionActions;

const initialState: SubscriptionState = {
  organizationPlan: null,
  isLoadingOrganizationPlan: false,
  subscriptionPlans: null,
  isLoadingPlans: false,
  subscriberData: null,
  isLoadingSubscriber: false,
  selectedPlan: null,
  billingCycle: 'yearly',
  selectedAddOnFeatures: [],
  isSubmittingQuote: false,
  isSubscribing: false,
  proratedCalculation: null,
  isCalculatingProrated: false,
  showUpgradeModal: false,
  showAddFeaturesModal: false,
  showQuoteModal: false,
  showSuccessModal: false,
  showFailureModal: false,
};

export const useSubscriptionStore = create<SubscriptionStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Organization Plan Actions
      fetchOrganizationPlan: async () => {
        try {
          set({ isLoadingOrganizationPlan: true });
          const organizationPlan = await getOrganizationPlan();
          set({ organizationPlan });
        } catch (error) {
          console.error('Error fetching organization plan:', error);
          toast.error('Failed to load organization plan. Please try again.');
        } finally {
          set({ isLoadingOrganizationPlan: false });
        }
      },

      // Subscription Plans Actions
      fetchSubscriptionPlans: async (includeInactive = true) => {
        try {
          set({ isLoadingPlans: true });
          const subscriptionPlans = await getSubscriptionPlans(includeInactive);
          set({ subscriptionPlans });
        } catch (error) {
          console.error('Error fetching subscription plans:', error);
          toast.error('Failed to load subscription plans. Please try again.');
        } finally {
          set({ isLoadingPlans: false });
        }
      },

      // Subscriber Actions
      fetchSubscriber: async (subscriberId: string) => {
        try {
          set({ isLoadingSubscriber: true });
          const subscriberData = await getSubscriber(subscriberId);
          set({ subscriberData });
        } catch (error) {
          console.error('Error fetching subscriber data:', error);
          toast.error('Failed to load subscriber data. Please try again.');
        } finally {
          set({ isLoadingSubscriber: false });
        }
      },

      // Email Validation
      validateUserEmail: async (
        email: string
      ): Promise<{
        isValid: boolean;
        canProceed: boolean;
        message: string;
      }> => {
        try {
          const result = await validateEmail(email);
          return {
            isValid: result.isValid,
            canProceed: result.canProceed,
            message: result.message,
          };
        } catch (error) {
          console.error('Error validating email:', error);
          return {
            isValid: false,
            canProceed: false,
            message: 'Email validation failed. Please try again.',
          };
        }
      },

      // UI Actions
      setSelectedPlan: (planId: string | null) => set({ selectedPlan: planId }),
      setBillingCycle: (cycle: BillingCycle) => set({ billingCycle: cycle }),
      setSelectedAddOnFeatures: (features: Feature[]) =>
        set({ selectedAddOnFeatures: features }),
      toggleAddOnFeature: (feature: Feature) => {
        const { selectedAddOnFeatures } = get();
        const isSelected = selectedAddOnFeatures.some(
          (f) => f.featureId === feature.featureId
        );

        if (isSelected) {
          // Remove feature
          set({
            selectedAddOnFeatures: selectedAddOnFeatures.filter(
              (f) => f.featureId !== feature.featureId
            ),
          });
        } else {
          // Add feature
          set({
            selectedAddOnFeatures: [...selectedAddOnFeatures, feature],
          });
        }
      },

      // Quote Actions
      submitQuote: async (data: QuoteRequest) => {
        try {
          set({ isSubmittingQuote: true });
          await submitQuoteRequest(data);

          // Store quote request in user data
          saveSubscriptionUserData({
            selectedPlan: 'custom',
            subscriptionCompleted: true,
            quoteRequest: {
              name: data.name,
              email: data.email,
              phone: data.phoneNumber,
              organisation: data.organizationName,
              designation: data.designation,
              requirements: data.requirements,
            },
          });

          set({ showQuoteModal: false, showSuccessModal: true });
          return true;
        } catch (error) {
          console.error('Error submitting quote:', error);
          set({ showQuoteModal: false, showFailureModal: true });
          toast.error(
            getErrorMessage(error) ||
              'Failed to submit quote request. Please try again.'
          );
          return false;
        } finally {
          set({ isSubmittingQuote: false });
        }
      },

      // Subscription Actions - Main function for paid plans
      subscribe: async (
        planId: string,
        billingPeriod: BillingCycle,
        billingDetails: {
          name: string;
          email: string;
          phoneNumber?: string;
          pincode?: string;
        },
        selectedAddOnFeatures: Feature[],
        paymentId: string
      ) => {
        try {
          set({ isSubscribing: true });

          // Get user data for role information
          const userData = getSubscriptionUserData();
          if (!userData?.role || !userData?.roleId) {
            toast.error('Role information is required for subscription');
            return false;
          }

          // Group add-on features by module
          const selectedAddOnFeaturesByModule = {
            MRD: selectedAddOnFeatures
              .filter(
                (f) =>
                  f.featureId.includes('mrd') ||
                  f.featureName.toLowerCase().includes('patient')
              )
              .map((f) => ({
                featureId: f.featureId,
                monthlyAmount: f.monthlyAmount || 0,
                yearlyAmount: f.yearlyAmount || 0,
              })),
            EMR: selectedAddOnFeatures
              .filter(
                (f) =>
                  f.featureId.includes('emr') ||
                  [
                    'dashboard',
                    'consultation',
                    'prescription',
                    'lifestyle',
                  ].some((key) => f.featureName.toLowerCase().includes(key))
              )
              .map((f) => ({
                featureId: f.featureId,
                monthlyAmount: f.monthlyAmount || 0,
                yearlyAmount: f.yearlyAmount || 0,
              })),
            Billing: selectedAddOnFeatures
              .filter(
                (f) =>
                  f.featureId.includes('billing') ||
                  ['appointment', 'lab', 'registration', 'master'].some((key) =>
                    f.featureName.toLowerCase().includes(key)
                  )
              )
              .map((f) => ({
                featureId: f.featureId,
                monthlyAmount: f.monthlyAmount || 0,
                yearlyAmount: f.yearlyAmount || 0,
              })),
          };

          const result = await subscribeAfterPayment({
            email: billingDetails.email,
            name: billingDetails.name,
            phoneNumber: billingDetails.phoneNumber,
            planId,
            billingType: billingPeriod,
            roleId: userData.roleId,
            userType: userData.role,
            userRole: userData.role,
            selectedAddOnFeatures: selectedAddOnFeaturesByModule,
            paymentId,
            Billing: [
              {
                name: billingDetails.name,
                email: billingDetails.email,
                pincode: billingDetails.pincode || '',
              },
            ],
          });

          if (result.success) {
            // Store subscription data
            saveSubscriptionUserData({
              selectedPlan: planId,
              subscriptionCompleted: true,
            });

            toast.success('Subscription successful! Welcome to ARCA EMR!');

            return true;
          } else {
            toast.error(
              result.message || 'Subscription failed. Please try again.'
            );
            return false;
          }
        } catch (error) {
          console.error('Error subscribing:', error);
          toast.error(
            getErrorMessage(error) || 'Subscription failed. Please try again.'
          );
          return false;
        } finally {
          set({ isSubscribing: false });
        }
      },

      // Alias for backward compatibility
      subscribePaidPlan: async (
        planId: string,
        billingPeriod: BillingCycle,
        billingDetails: {
          name: string;
          email: string;
          phoneNumber?: string;
        },
        selectedAddOnFeatures: Feature[],
        paymentId: string
      ) => {
        return get().subscribe(
          planId,
          billingPeriod,
          billingDetails,
          selectedAddOnFeatures,
          paymentId
        );
      },

      // Prorated Calculation Actions
      calculateProrated: async (data: {
        currentPlanId: string;
        newPlanId: string;
        billingPeriod: BillingCycle;
        currentPlanStartDate: string;
      }) => {
        try {
          set({ isCalculatingProrated: true });
          const calculation = await calculateProratedAmount(data);
          set({ proratedCalculation: calculation });
        } catch (error) {
          console.error('Error calculating prorated amount:', error);
          toast.error('Failed to calculate prorated amount. Please try again.');
        } finally {
          set({ isCalculatingProrated: false });
        }
      },

      // Trial Actions
      startTrial: async (data: {
        email: string;
        name: string;
        phoneNumber?: string | null;
        planId: string;
        billingType: 'monthly' | 'yearly';
        roleId: string;
        userType: string;
        userRole: string;
        Billing: Array<{
          name: string;
          email: string;
          pincode: string;
        }>;
      }) => {
        try {
          set({ isSubscribing: true }); // Reuse subscribing state for trial
          const result = await startTrial(data);

          if (result.success) {
            // Store trial data
            saveSubscriptionUserData({
              selectedPlan: data.planId,
              subscriptionCompleted: true,
            });

            toast.success('Trial started successfully! Welcome to ARCA EMR!');

            return true;
          } else {
            // Show actual error message from API as toast
            const errorMessage =
              result.message || 'Failed to start trial. Please try again.';
            toast.error(errorMessage);
            console.error('Trial failed:', result.message);
            return false;
          }
        } catch (error) {
          console.error('Error starting trial:', error);
          // Show error message as toast with fallback message
          const errorMessage =
            getErrorMessage(error) ||
            'Failed to start trial. Please try again.';
          toast.error(errorMessage);
          return false;
        } finally {
          set({ isSubscribing: false });
        }
      },

      // Change Plan Actions
      changePlanSubscription: async (data: {
        email: string;
        newPlanId: string;
        billingType: 'monthly' | 'yearly';
        selectedAddOnFeatures: {
          MRD: Array<{
            featureId: string;
            monthlyAmount: number;
            yearlyAmount: number;
          }>;
          EMR: Array<{
            featureId: string;
            monthlyAmount: number;
            yearlyAmount: number;
          }>;
          Billing: Array<{
            featureId: string;
            monthlyAmount: number;
            yearlyAmount: number;
          }>;
        };
        paymentId: string;
      }) => {
        try {
          set({ isSubscribing: true });
          const result = await changePlanSubscription(data);

          if (result.success) {
            // Refresh subscriber data to get updated plan
            const { subscriberData } = get();
            if (subscriberData?.id) {
              await get().fetchSubscriber(subscriberData.id);
            }

            toast.success('Plan changed successfully!');

            return true;
          } else {
            toast.error(
              result.message || 'Failed to change plan. Please try again.'
            );
            return false;
          }
        } catch (error) {
          console.error('Error changing plan:', error);
          toast.error(
            getErrorMessage(error) || 'Failed to change plan. Please try again.'
          );
          return false;
        } finally {
          set({ isSubscribing: false });
        }
      },

      // Modal Actions
      setShowUpgradeModal: (show: boolean) => set({ showUpgradeModal: show }),
      setShowAddFeaturesModal: (show: boolean) =>
        set({ showAddFeaturesModal: show }),
      setShowQuoteModal: (show: boolean) => set({ showQuoteModal: show }),
      setShowSuccessModal: (show: boolean) => set({ showSuccessModal: show }),
      setShowFailureModal: (show: boolean) => set({ showFailureModal: show }),

      // Reset Actions
      resetState: () => set(initialState),
    }),
    {
      name: 'subscription-store',
      partialize: (state) => ({
        selectedPlan: state.selectedPlan,
        billingCycle: state.billingCycle,
        selectedAddOnFeatures: state.selectedAddOnFeatures,
      }),
    }
  )
);
