import { toast } from 'sonner';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { useUserStore } from '@/store/userStore';

import { getPatientById } from '@/query/mrd/manage-patient/manage';
import {
  getPatientVitals,
  getSearchPatient,
} from '@/query/mrd/manage-patient/search-patient';

import { getErrorMessage } from '@/utils/error-message';
import formatVitals from '@/utils/mrd/manage-patient/format-vitals';

import { LOCAL_STORAGE_KEYS } from '@/constants/local-storage';

import {
  defaultVitalCardAttributes,
  VitalCardAttributes,
} from '@/components/vitals-card/types';

import { Patient, SearchPatient } from '@/types/mrd/manage-patient/patient';
import { Vitals } from '@/types/mrd/manage-patient/vitals';

import { useUpdateVitalStore } from './update-vital';

type MrdPatientSearchStoreState = {
  searchSelected: SearchPatient | null;
  patient: Patient | null;
  recentSearches: SearchPatient[];
  loading: boolean;
  vital: Vitals | null;
  vitalsLoading: boolean;
  calculatedVital: VitalCardAttributes[];
};

type MrdPatientSearchStoreActions = {
  setPatient: (patient: Patient) => void;
  searchPatient: (
    query: string,
    callback: (options: SearchPatient[]) => void
  ) => Promise<SearchPatient[]>;
  selectPatient: (patient: SearchPatient | null) => void;
  fetchVitals: (patientId: string) => Promise<void>;
  refreshPatient: () => void;
  setRecentPatient: (patient: SearchPatient) => void;
};

export type MrdPatientSearchStore = MrdPatientSearchStoreState &
  MrdPatientSearchStoreActions;

const initialState: MrdPatientSearchStoreState = {
  searchSelected: null,
  patient: null,
  recentSearches: [],
  loading: false,
  vital: null,
  vitalsLoading: false,
  calculatedVital: defaultVitalCardAttributes,
};

export const useMrdPatientSearch = create<MrdPatientSearchStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      setPatient: (patient) => {
        set({ patient });
      },
      setRecentPatient: (patient) => {
        const recentSearches = get().recentSearches;
        const updatedSearches = [
          patient,
          ...(recentSearches ?? []).filter(
            (search) => search?.id !== patient?.id
          ),
        ]?.slice(0, 10);
        set({ recentSearches: updatedSearches });
      },

      searchPatient: async (query, callback) => {
        const { data } = useUserStore.getState();
        const userId = data?.b2cUserId;
        const accountType = data?.accountType;
        const patients = await getSearchPatient(query, userId, accountType);
        callback(patients);
        return patients;
      },

      selectPatient: async (selectedPatient) => {
        if (!selectedPatient) {
          set({ loading: false, searchSelected: null, patient: null });
          return;
        }

        get().setRecentPatient(selectedPatient);
        set({ loading: true, searchSelected: selectedPatient });
        try {
          const patient = await getPatientById(selectedPatient.id);
          set({ patient, loading: false });
          get().fetchVitals(selectedPatient.id);
        } catch (error) {
          console.error(error);
          toast.error(getErrorMessage(error, 'Failed to load patient'));
          set({ loading: false, searchSelected: null });
        }
      },
      fetchVitals: async (patientId) => {
        set({ vitalsLoading: true });
        try {
          const vitals = await getPatientVitals(patientId);
          set({ vital: vitals[0] });
          set({
            calculatedVital: formatVitals({
              vital: vitals[0],
              prevVital: vitals[1],
            }),
          });
          useUpdateVitalStore.getState().vital = vitals[0];
        } catch (error) {
          console.error(error);
          toast.error(getErrorMessage(error, 'Failed to load vitals'));
        } finally {
          set({ vitalsLoading: false });
        }
      },
      refreshPatient: () => {
        const { patient } = get();
        if (patient?.id) {
          get().fetchVitals(patient.id);
        }
      },
    }),
    { name: LOCAL_STORAGE_KEYS.MRD_PATIENT_SEARCH }
  )
);
