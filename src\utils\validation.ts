import { isValidNumber } from 'aadhaar-validator';

import { keys } from './constants/keys';
import { regex } from './constants/regex';

const ALLOWED_KEYS = [
  keys.BACKSPACE,
  keys.DELETE,
  keys.ARROW_LEFT,
  keys.ARROW_RIGHT,
  keys.ARROW_UP,
  keys.ARROW_DOWN,
  keys.TAB,
  keys.ENTER,
];

const preventInvalidInput = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
  pattern: RegExp
) => {
  if (!ALLOWED_KEYS.includes(e.key) && !pattern.test(e.key)) {
    e.preventDefault();
  }
};

export const preventNonAlphabeticInput = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => preventInvalidInput(e, regex.ALPHABETIC_REGEX);

export const allowNumbersAndSpecialChars = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => preventInvalidInput(e, regex.NUMBERS_AND_SPECIAL_CHARS_REGEX);

export const restrictToNumbersAndAlphabetsAndDeletes = (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  if (!/[\dA-Za-z]/.test(e.key) && !['Backspace', 'Delete'].includes(e.key)) {
    e.preventDefault();
  }
};

export const restrictMobile = (maxLength: number) => {
  return (e: React.KeyboardEvent<HTMLInputElement>) => {
    const isCtrlOrCmd = e.ctrlKey || e.metaKey;
    if (ALLOWED_KEYS.includes(e.key) || (isCtrlOrCmd && e.key === 'v')) {
      return;
    }

    if (e.currentTarget.value.length >= maxLength) {
      e.preventDefault();
    }
    if (!/^\d$/.test(e.key)) {
      e.preventDefault();
    }
  };
};

export const allowOnlyNumbers = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => preventInvalidInput(e, regex.NUMBERS_ONLY_REGEX);

export const allowNumbersWithDecimals = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  // Allow: backspace, delete, tab, escape, enter, decimal point, numbers, navigation keys
  if (
    // Allow: Backspace, Delete, Tab, Escape, Enter, Decimal point
    [46, 8, 9, 27, 13, 110, 190].includes(e.keyCode) ||
    // Allow: Ctrl+A, Command+A
    (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey)) ||
    // Allow: Ctrl+C, Command+C
    (e.keyCode === 67 && (e.ctrlKey === true || e.metaKey)) ||
    // Allow: Ctrl+X, Command+X
    (e.keyCode === 88 && (e.ctrlKey === true || e.metaKey)) ||
    // Allow: home, end, left, right, down, up
    (e.keyCode >= 35 && e.keyCode <= 40) ||
    // Allow: numbers on top of keyboard or numpad
    (e.keyCode >= 48 && e.keyCode <= 57) ||
    (e.keyCode >= 96 && e.keyCode <= 105) ||
    // Allow: decimal point on numpad
    e.keyCode === 110 ||
    e.keyCode === 190
  ) {
    // Get the current value safely
    const currentValue =
      (e.target as HTMLInputElement | HTMLTextAreaElement).value || '';

    // Prevent multiple decimal points
    if ((e.key === '.' || e.key === ',') && currentValue.includes('.')) {
      e.preventDefault();
    }
    // Let it happen, don't do anything
    return;
  }
  // Ensure that it is a number and stop the keypress
  if (
    e.shiftKey ||
    ((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105))
  ) {
    e.preventDefault();
  }
};

export const allowOnlyNumbersOnPaste = (
  e: React.ClipboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  e.preventDefault();
};

export const allowAlphanumericInput = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => preventInvalidInput(e, regex.ALPHANUMERIC_REGEX);

export const handleNumericInput = (
  e: React.KeyboardEvent<HTMLInputElement>,
  allowedDecimals: number = 1
) => {
  const { key, currentTarget } = e;
  if (ALLOWED_KEYS.includes(key)) return;

  if (!regex.DIGIT_ONLY.test(key) && key !== '.') {
    e.preventDefault();
    return;
  }

  if (key === '.' && currentTarget.value.includes('.')) {
    e.preventDefault();
    return;
  }

  if (currentTarget.value.includes('.')) {
    const [, decimals] = currentTarget.value.split('.');
    if (decimals.length >= allowedDecimals) e.preventDefault();
  }
};

export const restrictMaxLength =
  (maxLength: number) =>
  (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (
      e.currentTarget.value.length >= maxLength &&
      !ALLOWED_KEYS.includes(e.key)
    ) {
      e.preventDefault();
    }
  };

export const allowNumbersTextAndDot = (
  e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  const { key } = e;

  preventInvalidInput(e, regex.ALPHANUMERIC_WITH_DOT_REGEX);

  if (key === '.' && e.currentTarget.value.includes('.')) {
    e.preventDefault();
  }
};

const createPatternValidator = (pattern: RegExp, message: string) => ({
  value: pattern,
  message,
});

export const mobileNumberPattern = (
  message = 'Provide a valid mobile number'
) => createPatternValidator(regex.MOBILE_NUMBER_PATTERN, message);

export const residentialPhonePattern = (
  message = 'Provide a valid residential phone number'
) => createPatternValidator(regex.RESIDENTIAL_MOBILE_NUMBER_PATTERN, message);

export const accountNumberPattern = (
  message = 'Account number must be between 8 and 20 digits'
) => createPatternValidator(regex.ACCOUNT_NUMBER_PATTERN, message);

export const emailPattern = (
  message = 'Entered value does not match email format'
) => createPatternValidator(regex.EMAIL_PATTERN, message);

export const aadharPattern = (message = 'Provide a valid Aadhar number') =>
  createPatternValidator(regex.AADHAR_PATTERN, message);

/**
 * Validates Indian phone numbers with country code 91
 * @param message Custom error message
 * @returns Validation function that checks for 12 digits including country code 91
 */
export const indianPhoneWithCode = (
  message = 'Enter a valid 10-digit phone number with country code +91'
) => {
  return (value: string) => {
    if (!value) return 'Phone number is required';
    const digits = value.replace(/\D/g, '');
    // Check for exactly 12 digits (including 91 country code)
    if (digits.length !== 12) return message;
    // Check if it starts with 91 (country code)
    if (!digits.startsWith('91')) return 'Phone number must start with +91';
    return true;
  };
};

/**
 * Handles keydown events for Indian phone number input with country code
 * @param e - The keyboard event
 * @returns void
 */
export type SetValueFunction = (
  value: string,
  options?: { shouldValidate?: boolean }
) => void;

/**
 * Handles paste events for Indian phone number input with country code
 * @param e - The clipboard event
 * @param setValue - Function to update the input value
 * @returns void
 */
export const handleIndianPhonePaste = <T extends Element>(
  e: React.ClipboardEvent<T>,
  setValue: SetValueFunction
): void => {
  e.preventDefault();
  const pastedText = e.clipboardData.getData('text/plain').replace(/\D/g, '');
  if (pastedText) {
    const cleanNumber = pastedText.replace(/^91/, '').slice(0, 10);
    const newValue = `+91${cleanNumber}`;
    setValue(newValue, { shouldValidate: true });
  }
};

export const handleIndianPhoneKeyDown = (
  e: React.KeyboardEvent<HTMLInputElement>
): void => {
  const input = e.currentTarget;
  const cursorPosition = input.selectionStart || 0;
  const value = input.value;

  // Allow: backspace, delete, tab, escape, enter, navigation keys
  if (
    [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'Home',
      'End',
    ].includes(e.key)
  ) {
    // Prevent deleting the country code
    if ((e.key === 'Backspace' || e.key === 'Delete') && cursorPosition <= 3) {
      e.preventDefault();
    }
    return;
  }

  // Allow: Ctrl+A, Ctrl+C, Ctrl+X
  if (e.ctrlKey || e.metaKey) {
    return;
  }

  // Ensure that it is a number and stop the keypress if not
  if (!/^\d$/.test(e.key)) {
    e.preventDefault();
    return;
  }

  // Limit to 10 digits after +91 (total 13 characters: +91XXXXXXXXXX)
  const digits = value.replace(/\D/g, '');
  if (digits.length >= 12) {
    e.preventDefault();
  }
};

export const aadharInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  const input = e.currentTarget;
  const value = input.value.replace(/-/g, '');

  if (ALLOWED_KEYS.includes(e.key)) {
    return;
  }

  restrictMobile(14)(e);

  if (!e.defaultPrevented) {
    setTimeout(() => {
      let formatted = value + e.key;
      formatted = formatted.match(/.{1,4}/g)?.join('-') || '';
      input.value = formatted;
    }, 0);
  }
};

export const aadharValidation = (val?: string) => {
  const sanitizedValue = val?.replace(/-/g, '');

  if (sanitizedValue?.length !== 12) {
    return 'AADHAR number is invalid';
  }
  if (!/^\d{12}$/.test(sanitizedValue)) {
    return 'AADHAR number is invalid';
  }

  if (/^(\d)\1{11}$/.test(sanitizedValue)) {
    return 'AADHAR number is invalid';
  }

  if (!isValidNumber(sanitizedValue)) {
    return 'AADHAR number is invalid';
  }
  return true;
};

export const validateOptionalNonZero = (label: string) => (value?: string) => {
  if (!value) return true;
  if (Number(value) === 0) return `Please enter a valid ${label}`;
  return true;
};

export const handleTextAreaBehavior = (
  e: React.KeyboardEvent<HTMLTextAreaElement>
) => {
  if (e.key === 'Enter') {
    e.preventDefault();
  }
};

type Options = {
  maxLength?: number;
  decimalPlaces?: number;
};

export const enforceNumericInput = (options?: Options) => {
  const { maxLength, decimalPlaces = 0 } = options || {};

  return (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    let value = input.value;

    value = value.replace(regex.NON_DIGIT_GLOBAL_REGEX, '');

    if (decimalPlaces > 0) {
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      if (decimalPlaces !== undefined && parts[1]?.length > decimalPlaces) {
        value = parts[0] + '.' + parts[1].slice(0, decimalPlaces);
      }
    } else {
      value = value.split('.')[0];
    }

    if (maxLength) {
      value = value.slice(0, maxLength);
    }

    input.value = value;
  };
};

export const enforceAlphabeticInput = (
  e: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  const input = e.currentTarget;
  const value = input.value.replace(regex.NON_ALPHABET_GLOBAL_REGEX, '');
  input.value = value;
};

export const enforceAlphabeticInputWithSpace = (
  e: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  const input = e.currentTarget;
  const value = input.value.replace(regex.NON_ALPHABET_WITH_SPACE_REGEX, '');
  input.value = value;
};

export const enforceAlphanumericInput = () => {
  return (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    let value = input.value;

    value = value.replace(/[^A-Za-z0-9]/g, '');

    input.value = value;
  };
};

export const enforceNumericWithHyphen = (maxDigits: number = 12) => {
  return (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;

    let value = input.value.replace(/[^0-9]/g, '');

    value = value.slice(0, maxDigits);

    value = value.match(/.{1,4}/g)?.join('-') || value;

    input.value = value;
  };
};

export const preventLeadingZero = (
  value: string,
  key: string,
  e: React.KeyboardEvent | React.ClipboardEvent
) => {
  const isLeadingZero = value.length === 0 && key === '0';
  if (isLeadingZero) {
    e.preventDefault();
  }
};
