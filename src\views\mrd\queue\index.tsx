'use client';

import React, { memo, useEffect } from 'react';

import { DatePicker } from '@/lib/common/date_picker';

import { fetchAppointments, useDoctorStore } from '@/store/mrd/queue/doctor';
import { useUserStore } from '@/store/userStore';

import colors from '@/utils/colors';
import { shouldHideBasedOnSubscription } from '@/utils/subscription';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import ConsultationSelect from '@/views/mrd/queue/consultation-select';
import QueueCard from '@/views/mrd/queue/queue-card';
import QueueList from '@/views/mrd/queue/queue-list';

import AccessDeniedCard from '@/core/components/access-denied-card';
import AppTitle from '@/core/components/app-title';
import LockedFeatureCard from '@/core/components/locked-feature-card';

const MrdPatientQueue = () => {
  const {
    consultingQueueItem: consultingAppointment,
    date,
    setDate,
    selectedDoctor,
    appointmentsByDoctor,
    loadSelectedDoctorAppointments,
    doctors,
  } = useDoctorStore();

  const { permissions = [], subscriptionFeatures, data } = useUserStore();
  const hasQueueManagePermission =
    permissions.includes(PERMISSION_KEYS.MRD_ACCESS) &&
    permissions.includes(PERMISSION_KEYS.MRD_PATIENT_QUEUE_MANAGE) &&
    permissions.includes(PERMISSION_KEYS.MRD_CONSULTATION_BOOK);

  // Check if should hide based on subscription
  const shouldHideQueue = shouldHideBasedOnSubscription(
    permissions,
    subscriptionFeatures,
    data,
    [
      PERMISSION_KEYS.MRD_ACCESS,
      PERMISSION_KEYS.MRD_PATIENT_QUEUE_MANAGE,
      PERMISSION_KEYS.MRD_CONSULTATION_BOOK,
    ],
    'mrd',
    true // requireAll
  );

  useEffect(() => {
    const unsubscribe = useUserStore.subscribe(
      (state) => state.currentAppointment,
      (currentAppointment) => {
        if (selectedDoctor) {
          if (currentAppointment) {
            useDoctorStore.getState().setConsultingQueueItem(
              {
                ...currentAppointment,
                time: currentAppointment.time,
                queueId: currentAppointment.queueId,
                queuePosition: currentAppointment.queuePosition,
                department: currentAppointment.department,
                patient: currentAppointment.patient,
                patientStatus: currentAppointment.patientStatus,
                status: currentAppointment.status,
              },
              selectedDoctor.id
            );
          } else {
            useDoctorStore
              .getState()
              .setConsultingQueueItem(null, selectedDoctor.id);
          }
          fetchAppointments(selectedDoctor.id);
        }
      }
    );

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'emrAction' && e.newValue && selectedDoctor) {
        const actionData = JSON.parse(e.newValue);

        if (actionData.doctorId === selectedDoctor.id) {
          if (
            actionData.action === 'cancel' ||
            actionData.action === 'markConsulted'
          ) {
            useDoctorStore
              .getState()
              .setConsultingQueueItem(null, selectedDoctor.id);
            fetchAppointments(selectedDoctor.id);
          }
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      unsubscribe();
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [selectedDoctor]);

  useEffect(() => {
    loadSelectedDoctorAppointments();
  }, [appointmentsByDoctor, loadSelectedDoctorAppointments]);

  useEffect(() => {
    if (
      data?.accountType === 'clinic' &&
      doctors.length > 0 &&
      !selectedDoctor
    ) {
      const currentUserDoctor = doctors.find((doc) => doc.id === data.id);
      if (currentUserDoctor) {
        useDoctorStore.getState().setSelectedDoctor(currentUserDoctor);
      }
    }
  }, [data?.accountType, doctors, selectedDoctor, data?.id]);

  useEffect(() => {
    if (selectedDoctor) {
      fetchAppointments(selectedDoctor.id);
    }
  }, [selectedDoctor, date]);

  return (
    <div className="w-[30%] h-full rounded-base shadow-base">
      <div
        className={`rounded-base border border-[${colors.common.ashGray}] bg-white flex flex-col box-border h-full`}
      >
        <div className="flex items-center justify-between p-base">
          <AppTitle>Consultation List</AppTitle>

          {hasQueueManagePermission && (
            <div className="flex gap-base">
              <ConsultationSelect />
              <DatePicker
                value={date}
                onChange={(newDate) => {
                  setDate(newDate);
                }}
              />
            </div>
          )}
        </div>
        <div
          className={`flex flex-col rounded-lg p-base bg-[#FCFCFC] h-full overflow-auto`}
        >
          {hasQueueManagePermission ? (
            <>
              <div className="flex items-center justify-between gap-8">
                <div className="font-medium text-base text-[#637D92] -tracking-[2.2%]">
                  Current Patient
                </div>
                <div className="flex items-center gap-base"></div>
              </div>
              <div className="py-base">
                {consultingAppointment ? (
                  <QueueCard
                    name={consultingAppointment.patient.name}
                    age={consultingAppointment.patient.age}
                    sex={consultingAppointment.patient.sex}
                    token={consultingAppointment.queuePosition}
                    status={consultingAppointment.status}
                    patientStatus={consultingAppointment.patientStatus}
                    type={consultingAppointment.department}
                    address={consultingAppointment.patient.address}
                    labTestStatus={consultingAppointment.labTestStatus}
                    statusReadOnly
                  />
                ) : (
                  <div className="h-8 border border-[#323F4940] rounded bg-[#DAE1E7]"></div>
                )}
              </div>

              <h2
                className={`
                  font-medium text-base text-[#637D92] mb-4
                `}
              >
                Patient Queue
              </h2>
              <div className="flex-grow h-full">
                <QueueList />
              </div>
            </>
          ) : shouldHideQueue ? (
            <AccessDeniedCard
              text="Patient Queue"
              iconClassName="w-12 h-12 mb-1"
              textClassName="text-md text-center px-2"
              containerClassName="gap-3"
            />
          ) : (
            <LockedFeatureCard
              text="You don't have permission to view or manage the  consultation list and patient queue"
              iconClassName="w-12 h-12 mb-1"
              textClassName="text-md text-center px-2"
              containerClassName="gap-3"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(MrdPatientQueue);
