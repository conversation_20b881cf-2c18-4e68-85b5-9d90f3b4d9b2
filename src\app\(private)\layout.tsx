'use client';

import { ReactNode, useEffect } from 'react';

import { usePathname } from 'next/navigation';

import { useUserStore } from '@/store/userStore';

import AuthGuard from '@/core/guard/AuthGuard';
import DepartmentGuard from '@/core/guard/DepartmentGuard';

export default function PrivateLayout({
  children,
}: Readonly<{ children: ReactNode }>) {
  const pathname = usePathname();

  useEffect(() => {
    // Call fetchUser on every page change to check subscription expiry
    useUserStore.getState().fetchUser();
  }, [pathname]);

  return (
    <AuthGuard>
      <DepartmentGuard>{children}</DepartmentGuard>
    </AuthGuard>
  );
}
