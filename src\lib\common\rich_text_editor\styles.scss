.rich-text-editor {
  & * {
    font-family: inherit;
  }

  .bn-editor.bn-cleaning-up {
    .bn-block-outer:has([data-content-type='bulletListItem'])
      .bn-block-group
      .bn-block-outer:has([data-content-type='bulletListItem']),
    .bn-block-outer:has([data-content-type='numberedListItem'])
      .bn-block-group
      .bn-block-outer:has([data-content-type='numberedListItem']) {
      display: none !important;
    }
  }

  /* Control side menu positioning to prevent visual artifacts */
  & > [data-floating-ui-focusable]:has(.bn-side-menu) {
    z-index: 0 !important;
  }

  /* Hide drag handle visual elements that create vertical lines */
  .bn-drag-handle {
    opacity: 0;
    transition: opacity 0.2s ease;

    /* Show on hover for functionality */
    &:hover,
    &:focus {
      opacity: 1;
    }
  }

  /* Show drag handle when hovering over the block */
  .bn-block-outer:hover .bn-drag-handle {
    opacity: 1;
  }

  &.white {
    .bn-editor {
      background-color: white;
    }
  }

  .mantine-Menu-dropdown {
    background-color: white;
    border: 1px solid black;
    box-shadow: none;

    .mantine-Menu-item {
      background-color: white;
      color: black;
    }
  }

  .bn-toolbar {
    background-color: white !important;
    border: 1px solid black !important;
    box-shadow: none !important;
    z-index: 1000 !important;
    display: block !important;
    visibility: visible !important;

    .mantine-Button-root,
    .mantine-ActionIcon-icon {
      color: black !important;
      background-color: white !important;
    }
  }

  /* Ensure formatting toolbar is always visible when text is selected */
  .bn-formatting-toolbar {
    display: block !important;
    visibility: visible !important;
    z-index: 1000 !important;
  }

  .bn-editor {
    padding-left: 1.25rem; /* pl-5 equivalent */
    padding-top: 0.25rem; /* py-1 equivalent */
    padding-bottom: 0.25rem;
    font-size: 0.875rem; /* text-sm */

    @media (min-width: 1280px) {
      font-size: 1rem; /* xl:text-base */
    }

    color: black;
    background-color: #e8ebed;
    border-radius: 5px;

    .ProseMirror {
      caret-color: #000 !important;
      /* Ensure cursor is always visible as vertical bar (|) */
      cursor: text !important;

      &:focus {
        outline: none;
        caret-color: #000 !important;
        cursor: text !important;
      }

      &[contenteditable='true'] {
        caret-color: #000 !important;
        cursor: text !important;

        &:focus {
          caret-color: #000 !important;
          cursor: text !important;
        }
      }
    }

    /* Ensure cursor is visible when editor is focused */
    .bn-editor:focus-within .ProseMirror {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Ensure cursor is visible on click/focus */
    .bn-editor .ProseMirror[contenteditable='true'] {
      caret-color: #000 !important;
      cursor: text !important;

      &:focus,
      &:focus-visible {
        caret-color: #000 !important;
        cursor: text !important;
      }
    }

    /* Ensure cursor is visible even when showing placeholder */
    .bn-editor .ProseMirror[contenteditable='true']:focus {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Ensure cursor is visible in empty blocks when focused */
    .bn-block-content[data-is-empty='true']
      .ProseMirror[contenteditable='true']:focus,
    .bn-block-content[data-is-empty-and-focused='true']
      .ProseMirror[contenteditable='true']:focus {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Additional rule to ensure cursor is visible on any click */
    .bn-editor .ProseMirror[contenteditable='true']:focus-visible,
    .bn-editor:focus-within .ProseMirror[contenteditable='true'] {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Force cursor visibility when editor container is clicked */
    .bn-editor:active .ProseMirror[contenteditable='true'],
    .bn-editor:focus .ProseMirror[contenteditable='true'] {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Ensure cursor is visible in empty paragraphs/blocks */
    .ProseMirror p.is-editor-empty,
    .ProseMirror .is-editor-empty {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Force cursor visibility when clicking on empty content */
    .bn-block-content[data-is-empty='true']
      .ProseMirror[contenteditable='true'],
    .bn-block-content[data-is-empty-and-focused='true']
      .ProseMirror[contenteditable='true'] {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Ensure cursor shows even when placeholder is visible */
    .ProseMirror[contenteditable='true']:focus p.is-editor-empty::before,
    .ProseMirror[contenteditable='true']:focus .is-editor-empty::before {
      /* Keep placeholder visible but ensure cursor is also visible */
    }

    /* Force cursor to show on any ProseMirror element when focused */
    .ProseMirror[contenteditable='true']:focus {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Additional specificity for empty blocks */
    .bn-block-outer
      .bn-block-content[data-is-empty='true']
      .ProseMirror[contenteditable='true']:focus,
    .bn-block-outer
      .bn-block-content[data-is-empty-and-focused='true']
      .ProseMirror[contenteditable='true']:focus {
      caret-color: #000 !important;
      cursor: text !important;
    }

    /* Darken placeholder text - Comprehensive selectors */
    .ProseMirror p.is-editor-empty::before,
    .ProseMirror h1.is-editor-empty::before,
    .ProseMirror h2.is-editor-empty::before,
    .ProseMirror h3.is-editor-empty::before,
    .bn-block-content[data-content-type='paragraph']
      .ProseMirror
      p.is-editor-empty::before,
    .bn-block-content .ProseMirror p.is-editor-empty::before,
    .bn-inline-content.is-editor-empty::before,
    .bn-block-content .ProseMirror .is-editor-empty::before,
    .bn-block-outer .ProseMirror p.is-editor-empty::before,
    .bn-block-outer .ProseMirror .is-editor-empty::before,
    .bn-block .ProseMirror p.is-editor-empty::before,
    .bn-block .ProseMirror .is-editor-empty::before,
    [data-content-type='paragraph'] .ProseMirror p.is-editor-empty::before,
    [data-content-type='paragraph'] .ProseMirror .is-editor-empty::before,
    * .ProseMirror p.is-editor-empty::before,
    * .ProseMirror .is-editor-empty::before {
      color: #111827 !important; /* Very dark, almost black */
      opacity: 1 !important;
      -webkit-text-fill-color: #111827 !important;
    }

    /* Reset all margins and padding to prevent accumulation, but preserve text alignment */
    .bn-block-group,
    .bn-block-outer,
    .bn-block {
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Be more careful with block-content to preserve alignment */
    .bn-block-content {
      margin: 0 !important;
      padding: 0 !important;
      /* Don't override text-align property */
    }

    /* Ensure consistent line height without extra spacing */
    .bn-inline-content {
      margin: 0 !important;
      padding: 0 !important;
      line-height: 1.5;
    }

    /* Ensure text alignment styles are preserved and displayed in edit mode */
    .bn-block-content[data-text-alignment='left'],
    .bn-block-content[data-text-alignment='left'] .bn-inline-content,
    .bn-block[data-text-alignment='left'] .bn-block-content,
    .bn-block[data-text-alignment='left'] .bn-inline-content {
      text-align: left !important;
    }

    .bn-block-content[data-text-alignment='center'],
    .bn-block-content[data-text-alignment='center'] .bn-inline-content,
    .bn-block[data-text-alignment='center'] .bn-block-content,
    .bn-block[data-text-alignment='center'] .bn-inline-content {
      text-align: center !important;
    }

    .bn-block-content[data-text-alignment='right'],
    .bn-block-content[data-text-alignment='right'] .bn-inline-content,
    .bn-block[data-text-alignment='right'] .bn-block-content,
    .bn-block[data-text-alignment='right'] .bn-inline-content {
      text-align: right !important;
    }

    .bn-block-content[data-text-alignment='justify'],
    .bn-block-content[data-text-alignment='justify'] .bn-inline-content,
    .bn-block[data-text-alignment='justify'] .bn-block-content,
    .bn-block[data-text-alignment='justify'] .bn-inline-content {
      text-align: justify !important;
    }

    /* Also support style-based alignment with higher specificity */
    .bn-block-content[style*='text-align: left'],
    .bn-block-content[style*='text-align: left'] .bn-inline-content,
    .bn-inline-content[style*='text-align: left'] {
      text-align: left !important;
    }

    .bn-block-content[style*='text-align: center'],
    .bn-block-content[style*='text-align: center'] .bn-inline-content,
    .bn-inline-content[style*='text-align: center'] {
      text-align: center !important;
    }

    .bn-block-content[style*='text-align: right'],
    .bn-block-content[style*='text-align: right'] .bn-inline-content,
    .bn-inline-content[style*='text-align: right'] {
      text-align: right !important;
    }

    .bn-block-content[style*='text-align: justify'],
    .bn-block-content[style*='text-align: justify'] .bn-inline-content,
    .bn-inline-content[style*='text-align: justify'] {
      text-align: justify !important;
    }

    /* BlockNote's default alignment classes */
    .bn-block-content.bn-text-align-left,
    .bn-block-content.bn-text-align-left .bn-inline-content {
      text-align: left !important;
    }

    .bn-block-content.bn-text-align-center,
    .bn-block-content.bn-text-align-center .bn-inline-content {
      text-align: center !important;
    }

    .bn-block-content.bn-text-align-right,
    .bn-block-content.bn-text-align-right .bn-inline-content {
      text-align: right !important;
    }

    .bn-block-content.bn-text-align-justify,
    .bn-block-content.bn-text-align-justify .bn-inline-content {
      text-align: justify !important;
    }

    /* Target paragraph elements directly for alignment */
    p.bn-inline-content[style*='text-align'] {
      display: block; /* Ensure paragraph display is preserved */
    }

    /* Ensure ProseMirror doesn't override alignment - more specific selectors */
    &.bn-editor .ProseMirror p[style*='text-align: left'],
    &.bn-editor .ProseMirror .bn-inline-content[style*='text-align: left'] {
      text-align: left !important;
    }

    &.bn-editor .ProseMirror p[style*='text-align: center'],
    &.bn-editor .ProseMirror .bn-inline-content[style*='text-align: center'] {
      text-align: center !important;
    }

    &.bn-editor .ProseMirror p[style*='text-align: right'],
    &.bn-editor .ProseMirror .bn-inline-content[style*='text-align: right'] {
      text-align: right !important;
    }

    &.bn-editor .ProseMirror p[style*='text-align: justify'],
    &.bn-editor .ProseMirror .bn-inline-content[style*='text-align: justify'] {
      text-align: justify !important;
    }

    /* Additional high-specificity rules for edit mode */
    &.bn-editor .bn-block-content p[style*='text-align: center'] {
      text-align: center !important;
    }

    &.bn-editor .bn-block-content p[style*='text-align: right'] {
      text-align: right !important;
    }

    &.bn-editor .bn-block-content p[style*='text-align: left'] {
      text-align: left !important;
    }

    &.bn-editor .bn-block-content p[style*='text-align: justify'] {
      text-align: justify !important;
    }

    /* Force alignment on all paragraph elements in edit mode */
    &.bn-editor p.bn-inline-content {
      &[style*='text-align: center'] {
        text-align: center !important;
        display: block !important;
      }

      &[style*='text-align: right'] {
        text-align: right !important;
        display: block !important;
      }

      &[style*='text-align: left'] {
        text-align: left !important;
        display: block !important;
      }

      &[style*='text-align: justify'] {
        text-align: justify !important;
        display: block !important;
      }
    }

    /* Override any BlockNote default styles */
    &.bn-editor
      .bn-block-outer
      .bn-block
      .bn-block-content
      .bn-inline-content[style*='text-align'] {
      text-align: inherit !important;
    }

    /* Target the contenteditable area directly */
    &.bn-editor [contenteditable='true'] {
      p[style*='text-align: center'] {
        text-align: center !important;
      }

      p[style*='text-align: right'] {
        text-align: right !important;
      }

      p[style*='text-align: left'] {
        text-align: left !important;
      }

      p[style*='text-align: justify'] {
        text-align: justify !important;
      }
    }

    /* Force alignment with maximum specificity - this should override everything */
    &.bn-editor .bn-block-content[data-text-alignment='center'],
    &.bn-editor .bn-block-content[data-text-alignment='center'] *,
    &.bn-editor .bn-block-content[style*='text-align: center'],
    &.bn-editor .bn-block-content[style*='text-align: center'] * {
      text-align: center !important;
    }

    &.bn-editor .bn-block-content[data-text-alignment='right'],
    &.bn-editor .bn-block-content[data-text-alignment='right'] *,
    &.bn-editor .bn-block-content[style*='text-align: right'],
    &.bn-editor .bn-block-content[style*='text-align: right'] * {
      text-align: right !important;
    }

    &.bn-editor .bn-block-content[data-text-alignment='left'],
    &.bn-editor .bn-block-content[data-text-alignment='left'] *,
    &.bn-editor .bn-block-content[style*='text-align: left'],
    &.bn-editor .bn-block-content[style*='text-align: left'] * {
      text-align: left !important;
    }

    &.bn-editor .bn-block-content[data-text-alignment='justify'],
    &.bn-editor .bn-block-content[data-text-alignment='justify'] *,
    &.bn-editor .bn-block-content[style*='text-align: justify'],
    &.bn-editor .bn-block-content[style*='text-align: justify'] * {
      text-align: justify !important;
    }

    /* Hide empty blocks that only contain trailing breaks - but preserve content */
    .bn-block-outer {
      /* Only hide blocks that are truly empty (no text content) */
      &:empty {
        display: none !important;
      }
    }

    /* Hide empty list items that might appear during cleanup to prevent flash */
    .bn-block-outer:has([data-content-type='bulletListItem']),
    .bn-block-outer:has([data-content-type='numberedListItem']) {
      .bn-block-content[data-content-type='bulletListItem']:has(
          p.bn-inline-content:empty:only-child
        ),
      .bn-block-content[data-content-type='bulletListItem']:has(
          p.bn-inline-content:has(
              br.ProseMirror-trailingBreak:only-child
            ):only-child
        ),
      .bn-block-content[data-content-type='numberedListItem']:has(
          p.bn-inline-content:empty:only-child
        ),
      .bn-block-content[data-content-type='numberedListItem']:has(
          p.bn-inline-content:has(
              br.ProseMirror-trailingBreak:only-child
            ):only-child
        ) {
        /* Only hide if it doesn't have nested content */
        &:not(
            :has(
              .bn-block-group
                .bn-block-outer:has([data-content-type='bulletListItem'])
            )
          ):not(
            :has(
              .bn-block-group
                .bn-block-outer:has([data-content-type='numberedListItem'])
            )
          ) {
          opacity: 0;
          height: 0;
          overflow: hidden;
        }
      }
    }

    /* Hide paragraphs that only contain trailing breaks */
    .bn-inline-content {
      /* Hide empty paragraphs */
      &:empty {
        display: none !important;
      }

      /* Hide paragraphs with only a trailing break and no text */
      &:has(br.ProseMirror-trailingBreak:only-child) {
        min-height: 0 !important;
        line-height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
      }
    }

    /* Ensure blocks with actual text content are always visible */
    .bn-inline-content:not(:empty):not(
        :has(br.ProseMirror-trailingBreak:only-child)
      ) {
      display: block !important;
      min-height: auto !important;
      line-height: 1.5 !important;
    }
  }

  .bn-side-menu {
    & .mantine-UnstyledButton-root:not(.mantine-Menu-item):hover {
      background-color: white;
    }
  }
}

.rich-text-editor a {
  color: blue !important;
}

/* Global placeholder darkening for BlockNote - Ultra aggressive selectors */
.rich-text-editor .ProseMirror p.is-editor-empty::before,
.rich-text-editor .ProseMirror .is-editor-empty::before,
.rich-text-editor .bn-block-content .ProseMirror p.is-editor-empty::before,
.rich-text-editor .bn-block-content .ProseMirror .is-editor-empty::before,
.rich-text-editor .bn-inline-content.is-editor-empty::before,
.rich-text-editor .bn-block-outer .ProseMirror p.is-editor-empty::before,
.rich-text-editor .bn-block-outer .ProseMirror .is-editor-empty::before,
.rich-text-editor .bn-block .ProseMirror p.is-editor-empty::before,
.rich-text-editor .bn-block .ProseMirror .is-editor-empty::before,
.rich-text-editor
  [data-content-type='paragraph']
  .ProseMirror
  p.is-editor-empty::before,
.rich-text-editor
  [data-content-type='paragraph']
  .ProseMirror
  .is-editor-empty::before,
.rich-text-editor .bn-editor .ProseMirror p.is-editor-empty::before,
.rich-text-editor .bn-editor .ProseMirror .is-editor-empty::before,
.rich-text-editor * .ProseMirror p.is-editor-empty::before,
.rich-text-editor * .ProseMirror .is-editor-empty::before {
  color: #111827 !important; /* Very dark, almost black */
  opacity: 1 !important;
  -webkit-text-fill-color: #111827 !important;
}

.non-editable-content {
  font-family: inherit;
  font-size: inherit;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;

  /* Prevent any spacing accumulation */
  & * {
    margin: 0 !important;
  }

  /* Ensure text alignment is preserved in non-editable content */
  .bn-block-content[data-text-alignment='left'] {
    text-align: left !important;
  }

  .bn-block-content[data-text-alignment='center'] {
    text-align: center !important;
  }

  .bn-block-content[data-text-alignment='right'] {
    text-align: right !important;
  }

  .bn-block-content[data-text-alignment='justify'] {
    text-align: justify !important;
  }

  /* Also support style-based alignment in view mode */
  [style*='text-align: left'] {
    text-align: left !important;
  }

  [style*='text-align: center'] {
    text-align: center !important;
  }

  [style*='text-align: right'] {
    text-align: right !important;
  }

  [style*='text-align: justify'] {
    text-align: justify !important;
  }
}

.non-editable-content .bn-block-group {
  display: block;
  margin: 0;
  padding: 0;
}

.non-editable-content .bn-block-outer {
  display: block;
  position: relative;
  margin: 0 !important;
  padding: 0 !important;
}

.non-editable-content .bn-block {
  display: block;
}

.non-editable-content .bn-block-content[data-content-type='numberedListItem'] {
  display: flex;
  padding-left: 1.5rem;
  position: relative;
}

.non-editable-content
  .bn-block-content[data-content-type='numberedListItem']::before {
  content: attr(data-index) '.';
  position: absolute;
  left: 0;
  font-weight: normal;
  color: inherit;
}

/* Ultimate fallback for any placeholder - catch all */
.rich-text-editor ::before {
  &[class*='placeholder'],
  &[class*='empty'] {
    color: #111827 !important;
    opacity: 1 !important;
    -webkit-text-fill-color: #111827 !important;
  }
}

/* Additional aggressive selector - target any ProseMirror empty state */
.rich-text-editor .ProseMirror [class*='empty']::before,
.rich-text-editor .ProseMirror [class*='placeholder']::before {
  color: #111827 !important;
  opacity: 1 !important;
  -webkit-text-fill-color: #111827 !important;
}

/* Global rule to ensure cursor (vertical bar |) is always visible when field is clicked/focused */
.rich-text-editor .ProseMirror[contenteditable='true'] {
  caret-color: #000 !important;
  cursor: text !important;
  /* Ensure cursor is visible even in empty state */
  min-height: 1px;
}

.rich-text-editor .ProseMirror[contenteditable='true']:focus,
.rich-text-editor .ProseMirror[contenteditable='true']:focus-visible,
.rich-text-editor:focus-within .ProseMirror[contenteditable='true'] {
  caret-color: #000 !important;
  cursor: text !important;
  /* Force cursor visibility on focus */
  outline: none !important;
}

/* Ensure cursor is visible in completely empty editors */
.rich-text-editor .ProseMirror[contenteditable='true']:empty:focus,
.rich-text-editor .ProseMirror[contenteditable='true']:focus:empty {
  caret-color: #000 !important;
  cursor: text !important;
}

/* Force cursor visibility for empty paragraphs when focused */
.rich-text-editor .ProseMirror[contenteditable='true']:focus p:empty,
.rich-text-editor .ProseMirror[contenteditable='true']:focus p.is-editor-empty {
  caret-color: #000 !important;
  cursor: text !important;
  min-height: 1.5em;
}
