import React from 'react';

import { useLockedFeatureClick } from '@/hooks/use-locked-feature-click';

import YellowLockIcon from '@/assets/svg/YellowLockIcon';

import FeatureTooltip from '@/core/components/feature-tooltip';

interface LockedFeatureCardProps {
  text: string;
  iconClassName?: string;
  textClassName?: string;
  containerClassName?: string;
  fullHeight?: boolean;
  horizontal?: boolean;
  onClick?: () => void;
  children?: React.ReactNode;
}

const LockedFeatureCard: React.FC<LockedFeatureCardProps> = ({
  text,
  iconClassName = 'w-5 h-5 mb-1',
  textClassName = 'text-xs text-center p-2',
  containerClassName = '',
  fullHeight = true,
  horizontal = false,
  onClick,
  children,
}) => {
  const {
    handleLockedFeatureClick,
    isClickable,
    tooltipMessage,
    isClinicAccount,
  } = useLockedFeatureClick();

  // For clinic accounts, show the actual content instead of the lock
  if (isClinicAccount && children) {
    return <>{children}</>;
  }

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (isClickable) {
      handleLockedFeatureClick();
    }
  };

  return (
    <div
      className={`flex ${horizontal ? 'flex-row items-center' : 'flex-col items-center justify-center'} ${fullHeight ? 'h-full' : ''} bg-green-200 rounded-base ${isClickable ? 'cursor-pointer' : 'cursor-not-allowed'} ${horizontal ? 'gap-2' : 'gap-1'} ${containerClassName}`}
      style={{
        background:
          'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(194, 233, 254, 0.8) 100%)',
      }}
      onClick={handleClick}
    >
      <FeatureTooltip title={tooltipMessage} placement="auto">
        <YellowLockIcon className={iconClassName} />
      </FeatureTooltip>
      <FeatureTooltip title={tooltipMessage} placement="auto">
        <span className={textClassName}>{text}</span>
      </FeatureTooltip>
    </div>
  );
};

export default LockedFeatureCard;
