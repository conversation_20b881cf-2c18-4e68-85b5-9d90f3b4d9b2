'use client';

import { useState, useEffect } from 'react';

import { useRouter } from 'next/navigation';

import Loading from '@/lib/common/loading';

import { useSubscriptionStore } from '@/store/subscription';
import { useUserStore } from '@/store/userStore';

import {
  getSubscriptionUserData,
  OrganizationPlan,
  calculateTotalWithTax,
  getFormattedExpiryText,
} from '@/utils/subscription';

import { routes } from '@/constants/routes';

import MonthlyYearlyToggle from '@/views/subscription/components/MonthlyYearlyToggle';
import SubscriptionLoading from '@/views/subscription/components/SubscriptionLoading';
import { Plan } from '@/views/subscription/PlanCard';

import AppModal from '@/core/components/app-modal';

import SmallPlanGrid from './small-plan-cards';

const PaymentsAndPlans = () => {
  const router = useRouter();
  const [userData, setUserData] = useState<any>(null);
  const [showPriceBreakupModal, setShowPriceBreakupModal] = useState(false);

  const {
    subscriptionPlans,
    organizationPlan,
    subscriberData,
    isLoadingPlans,
    isLoadingOrganizationPlan,
    isLoadingSubscriber,
    fetchSubscriptionPlans,
    fetchOrganizationPlan,
    fetchSubscriber,
    selectedPlan,
    setSelectedPlan,
    billingCycle,
    setBillingCycle,
    subscribe,
    isSubscribing,
  } = useSubscriptionStore();

  // Use UserStore for subscriber ID
  const { data: userStoreData } = useUserStore();

  // Convert API plans to Plan format
  const convertApiPlansToPlans = (apiPlans: OrganizationPlan[]): Plan[] => {
    return apiPlans
      .filter((plan) => {
        // Filter plans based on billing cycle
        if (billingCycle === 'monthly') {
          return plan.validity === 'Monthly' || plan.validity === 'Both';
        } else {
          return plan.validity === 'Yearly' || plan.validity === 'Both';
        }
      })
      .map((plan) => {
        const hasAddOnFeatures = Object.values(plan.addOnFeatures).some(
          (features) => features.length > 0
        );

        const price =
          billingCycle === 'monthly'
            ? (plan.totalMonthlyBasicAmount ?? plan.monthlyTotal)
            : (plan.totalYearlyBasicAmount ?? plan.yearlyTotal);

        return {
          id: plan.id,
          name: plan.planName,
          subtitle:
            billingCycle === 'monthly' ? 'Billed Monthly' : 'Billed Annually',
          monthlyPrice: plan.totalMonthlyBasicAmount ?? plan.monthlyTotal,
          yearlyPrice: plan.totalYearlyBasicAmount ?? plan.yearlyTotal,
          // Keep the original features structure for hierarchical display
          features: [], // Will be populated by the new hierarchical structure
          buttonText: 'Buy Now',
          buttonColor: price === 0 ? '#0496E1' : '#0EA5E9',
          popular: false,
          hasAddOnFeatures,
          isOrganizationPlan: false,
          apiPlan: plan,
        };
      });
  };

  const convertOrganizationPlanToPlan = (orgPlan: OrganizationPlan): Plan => {
    const hasAddOnFeatures = Object.values(orgPlan.addOnFeatures).some(
      (features) => features.length > 0
    );

    return {
      id: orgPlan.id,
      name: orgPlan.planName,
      subtitle:
        billingCycle === 'monthly' ? 'Billed Monthly' : 'Billed Annually',
      monthlyPrice: 0, // Hide price for organization plan
      yearlyPrice: 0, // Hide price for organization plan
      features: [], // Empty features for organization plans (will show description instead)
      buttonText: 'Get Quote', // Always show Get Quote for organization plan
      buttonColor: '#0496E1',

      hasAddOnFeatures,
      isOrganizationPlan: true,
      apiPlan: orgPlan,
    };
  };

  // Convert regular subscription plans
  const regularPlans = subscriptionPlans
    ? convertApiPlansToPlans(subscriptionPlans.plans)
    : [];

  // Filter out plans that cost less than current plan (totalWithTax) when viewing from profile
  const currentPlan = subscriberData?.activeSubscription;
  const currentPlanBaseAmount =
    subscriberData?.activeSubscription?.totalAmount || 0;
  const currentPlanTotalWithTax = calculateTotalWithTax(currentPlanBaseAmount);

  const filteredPlans = regularPlans.filter((plan) => {
    // Get the actual API plan amounts for comparison
    const apiPlan = plan.apiPlan;
    const planMonthlyAmount =
      apiPlan?.totalMonthlyBasicAmount ??
      apiPlan?.monthlyTotal ??
      plan.monthlyPrice;
    const planYearlyAmount =
      apiPlan?.totalYearlyBasicAmount ??
      apiPlan?.yearlyTotal ??
      plan.yearlyPrice;

    // Get the price for current billing cycle
    const planPrice =
      billingCycle === 'monthly' ? planMonthlyAmount : planYearlyAmount;

    // If this is the current plan
    if (plan.id === currentPlan?.planId) {
      // Only show current plan if billing cycle matches subscription billing type
      return billingCycle === currentPlan?.billingType;
    }

    // For other plans, hide if price is less than current plan's totalWithTax
    // Show plans with same price or higher (allows plan changes at same price level)
    return planPrice >= currentPlanTotalWithTax;
  });

  // Sort regular plans by price (ascending order), but ensure current plan is first
  const sortedRegularPlans = filteredPlans.sort((a, b) => {
    // If a is current plan, it should come first
    if (a.id === currentPlan?.planId) return -1;
    // If b is current plan, it should come first
    if (b.id === currentPlan?.planId) return 1;

    // Sort by the appropriate price based on billing cycle
    const priceA = billingCycle === 'monthly' ? a.monthlyPrice : a.yearlyPrice;
    const priceB = billingCycle === 'monthly' ? b.monthlyPrice : b.yearlyPrice;
    return priceA - priceB;
  });

  // Convert organization plan if available
  const organizationPlanCard = organizationPlan
    ? convertOrganizationPlanToPlan(organizationPlan)
    : null;

  // Combine plans - regular plans first (sorted), then organization plan at the end
  const allPlans = organizationPlanCard
    ? [...sortedRegularPlans, organizationPlanCard]
    : sortedRegularPlans;

  useEffect(() => {
    // Fetch subscription plans
    fetchSubscriptionPlans(true);

    // Fetch organization plan
    fetchOrganizationPlan();

    // Get user data from UserStore
    if (userStoreData && userStoreData.subscriberId) {
      // Fetch subscriber data using subscriberId from UserStore
      fetchSubscriber(userStoreData.subscriberId);
      setUserData(userStoreData);
    } else {
      // Fallback to subscription user data for testing
      const subscriptionUserData = getSubscriptionUserData();
      if (subscriptionUserData) {
        setUserData(subscriptionUserData);
      } else {
        // Create default user data for testing
        const defaultUserData = {
          signupCompleted: true,
          establishment: 'hospital',
          role: 'admin',
        };
        setUserData(defaultUserData);
      }
    }
  }, [
    router,
    fetchSubscriptionPlans,
    fetchOrganizationPlan,
    fetchSubscriber,
    userStoreData,
  ]);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleSubscribe = async (planId: string) => {
    // Find the plan to check if it's free (cost 0, not organization plan)
    const plan = allPlans.find((p) => p.id === planId);

    // According to requirements:
    // - Free plans (cost 0, not organization) should use startTrial API
    // - Paid plans should use subscribeAfterPayment API
    // - The subscribeToPlan API should be completely removed

    if (
      plan &&
      !plan.isOrganizationPlan &&
      (plan.monthlyPrice === 0 || plan.yearlyPrice === 0)
    ) {
      // Free plans are handled by PlanCard component via SubscriptionDetailsModal
      // Don't call subscription API here to avoid duplicate calls
      return;
    }

    // For paid plans, the logic is handled in PlanCard component
    // No direct subscription call needed here as it's handled via modal flow
    return;
  };

  const handleGetQuote = () => {
    router.push(routes.SUBSCRIPTION_CUSTOM_PRICING);
  };
  // Calculate total amount with 18% tax for display
  const displayAmount = subscriberData?.activeSubscription?.totalAmount || 0;
  const totalWithTax = calculateTotalWithTax(displayAmount);

  // Calculate price breakup for current plan
  const calculatePriceBreakup = () => {
    const addOnFeatures =
      subscriberData?.activeSubscription?.addOnFeatures || {};
    const billingType =
      subscriberData?.activeSubscription?.billingType || 'monthly';

    // Sum add-on features amounts
    let featureAddedAmount = 0;
    Object.values(addOnFeatures).forEach((moduleFeatures: any) => {
      if (Array.isArray(moduleFeatures)) {
        moduleFeatures.forEach((feature: any) => {
          const amount =
            billingType === 'monthly'
              ? feature.monthlyAmount || 0
              : feature.yearlyAmount || 0;
          featureAddedAmount += amount;
        });
      }
    });

    // Premium amount is totalAmount minus feature added amount
    const premiumAmount = displayAmount - featureAddedAmount;
    const subTotal = displayAmount;
    const tax = subTotal * 0.18;
    const total = subTotal + tax;

    return {
      premiumAmount,
      featureAddedAmount,
      subTotal,
      tax,
      total,
    };
  };

  const priceBreakup = calculatePriceBreakup();

  const isLoading = isLoadingPlans || isLoadingOrganizationPlan;

  if (!userData || isLoading) {
    return <SubscriptionLoading />;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Heading with specified styling */}
      <div className="mb-2">
        <h1 className="text-[18px] font-[700] text-[#001926]">
          Payments & Plans
        </h1>
      </div>

      {/* Current User Plan Section - Now shows real data */}
      {isLoadingSubscriber ? (
        <div className="mb-4 p-6 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center justify-center py-8">
            <Loading />
          </div>
        </div>
      ) : subscriberData && subscriberData.activeSubscription ? (
        <div className=" px-4 py-2 mb-2 border border-gray-200 rounded-lg bg-gray-50">
          {/* Plan Title */}
          <h3 className="text-[16px] font-[500] text-[#012436] mb-1">
            {subscriberData.activeSubscription.planName}
            <span className="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full ms-4">
              Current Plan
            </span>
          </h3>

          {/* Expiry Date - Dynamic days calculation */}
          <p className="text-[14px] font-[400] text-red-600 mb-2">
            {getFormattedExpiryText(
              subscriberData.activeSubscription.endDate,
              subscriberData.activeSubscription.billingType
            )}
          </p>

          {/* Plan Cost and Billing Type */}
          <div className="flex items-center gap-4 ">
            <p className="text-4xl font-[600] text-[#012436]">
              ₹{totalWithTax}
            </p>
            <button
              onClick={() => setShowPriceBreakupModal(true)}
              className="text-[14px] font-[400] text-[#162B60] underline"
            >
              View price breakup
            </button>
          </div>
        </div>
      ) : (
        <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <p className="text-sm text-gray-600">
            {subscriberData
              ? 'No active subscription found.'
              : 'Loading subscription information...'}
          </p>
        </div>
      )}

      {/* Monthly/Yearly Toggle */}
      <MonthlyYearlyToggle
        billingCycle={billingCycle}
        onBillingCycleChange={setBillingCycle}
        className="mb-4"
      />

      {/* Fixed Plan Cards Section */}
      <div className="flex-1 min-h-0">
        <SmallPlanGrid
          plans={allPlans}
          billingCycle={billingCycle}
          selectedPlan={selectedPlan}
          onPlanSelect={handlePlanSelect}
          onGetQuote={handleGetQuote}
          onSubscribe={handleSubscribe}
          isLoading={false}
          isCompact={true}
          isFromProfile={true}
          currentPlanId={subscriberData?.activeSubscription?.planId}
          plansPerPage={3}
        />
      </div>

      {/* Price Breakup Modal */}
      <AppModal
        open={showPriceBreakupModal}
        onClose={() => setShowPriceBreakupModal(false)}
        title="Price breakup"
        classes={{
          root: 'w-[350px] max-h-[90vh]',
          body: 'p-8',
        }}
      >
        <div className="flex flex-col h-[55vh] relative">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto px-5">
            <div className="space-y-6">
              <div className="flex justify-between">
                <span className="text-[14px] font-normal text-black">
                  Base Plan Amount{' '}
                </span>
                <span className="text-[14px] font-normal text-black">
                  ₹{priceBreakup.premiumAmount.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-[14px] font-normal text-black">
                  Feature added
                </span>
                <span className="text-[14px] font-normal text-black">
                  ₹{priceBreakup.featureAddedAmount.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between border-t pt-2">
                <span className="text-[14px] font-medium text-black">
                  Sub Total
                </span>
                <span className="text-[14px] font-medium text-black">
                  ₹{priceBreakup.subTotal.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-[14px] font-normal text-black">Tax</span>
                <span className="text-[14px] font-normal text-black">
                  ₹{priceBreakup.tax.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Fixed Total Amount at bottom */}
          <div className="absolute bottom-0 left-0 right-0 bg-white border-t pt-4">
            <div className="flex justify-between">
              <span className="text-[16px] font-bold text-black">
                Total Amount
              </span>
              <span className="text-[16px] font-bold text-black">
                ₹{priceBreakup.total.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </AppModal>
    </div>
  );
};

export default PaymentsAndPlans;
