import { VitalStatus } from '@/components/vitals-card/types';

import { arcaAxios } from '@/core/lib/interceptor';
import { SearchPatient } from '@/types/mrd/manage-patient/patient';
import { Vitals } from '@/types/mrd/manage-patient/vitals';

type VitalsResponse = {
  vitals: Vitals;
  created_on: string;
  vitalStatuses: {
    height: VitalStatus;
    weight: VitalStatus;
    bmi: VitalStatus;
    pulse: VitalStatus;
    rr: VitalStatus;
    bp: VitalStatus;
    temperature: VitalStatus;
    spO2: VitalStatus;
    sbp: VitalStatus;
    dbp: VitalStatus;
  };
};

export const getSearchPatient = async (
  query: string,
  userId?: string,
  accountType?: string
): Promise<SearchPatient[]> => {
  const baseWhere = `
        CONTAINS(c.contact.phone, '${query}')
          OR CONTAINS(LOWER(c.id), '${query.toLowerCase()}')
          OR CONTAINS(LOWER(c.name), '${query.toLowerCase()}')
          OR CONTAINS(LOWER(c.aadhar), '${query.toLowerCase()}')
          OR CONTAINS(LOWER(c.abha), '${query.toLowerCase()}')
      `;

  let whereClause = baseWhere;

  if (accountType === 'clinic' && userId) {
    whereClause = ` c.created_by = '${userId}' AND (${baseWhere}) `;
  }

  const body: any = {
    pagesize: 10,
    continuetoken: null,
    query: `SELECT c.id, c.name FROM c WHERE ${whereClause}`,
  };

  const { data } = await arcaAxios.post(`/patient/search`, body);

  return data?.items ?? [];
};

export const getPatientVitals = async (id: string): Promise<Vitals[]> => {
  const { data } = await arcaAxios.get<VitalsResponse[]>(
    `/patient/vitals?patientId=${id}`
  );
  const vitals = data.map((vital) => ({
    ...vital.vitals,
    createdAt: vital.created_on,
    vitalStatuses: {
      ...vital.vitalStatuses,
    },
    ageGroup: vital.vitals.ageGroup,
  }));
  return vitals;
};

export const updateVitals = async (
  patientId: string,
  vitals: Partial<Vitals>
) => {
  return arcaAxios.post(`/patient/vitals?patientId=${patientId}`, {
    vitals,
  });
};
