'use client';

import { ReactNode } from 'react';
import { useEffect } from 'react';

import { useIsAuthenticated } from '@azure/msal-react';

import { useRouter } from 'next/navigation';

import SessionIdleMonitor from '@core/providers/SessionIdleMonitor';

import Loading from '@/lib/common/loading';

import { useUserStore } from '@/store/userStore';

export default function AuthGuard({ children }: { children: ReactNode }) {
  const isAuthenticated = useIsAuthenticated();
  const router = useRouter();
  const { data } = useUserStore();

  useEffect(() => {
    if (!isAuthenticated) {
      if (typeof window !== 'undefined') {
        localStorage.setItem(
          'redirectTo',
          window.location.pathname + window.location.search
        );
      }
      router.push(`/login`);
    } else if (data?.subscriptionExpiryDate) {
      const expiryDate = new Date(data.subscriptionExpiryDate);
      const now = new Date();
      if (expiryDate < now) {
        // Store accountType for unauthorized page
        if (typeof window !== 'undefined') {
          localStorage.setItem('subscriptionAccountType', data.accountType);
        }
        // Redirect based on accountType
        if (data.accountType === 'clinic') {
          router.push('/unauthorized?reason=subscription_expired');
        } else {
          router.push('/unauthorized');
        }
      }
    }
  }, [isAuthenticated, router, data]);

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen w-screen">
        <Loading />
      </div>
    );
  }

  return (
    <>
      <SessionIdleMonitor />
      {children}
    </>
  );
}
